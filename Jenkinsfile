pipeline {
  agent any
  environment {
    // Docker registry configuration
    CMC_PRIVATE_REGISTRY = '**************:5000' // private registry
    CMC_PUBLIC_REGISTRY = '************:5001' // public registry
    ECR_REGISTRY = '187091248012.dkr.ecr.us-east-1.amazonaws.com'
    DOCKER_IMAGE = 'cmc/ts/cagent/cagent'
    CMC_REPOSITORY = "${CMC_PRIVATE_REGISTRY}/${DOCKER_IMAGE}"
    ECR_REPOSITORY = "${ECR_REGISTRY}/${CUSTOMER_NAME}/cagent"
    CUSTOMER_NAME = "kyocera" // customer name, change as needed

    // Base image for cache usage
    BASE_IMAGE = "${CMC_PRIVATE_REGISTRY}/cmc/ts/cagent/cagent:base"

    // SSH credentials => Add at project folder level (Shared for all customers)
    BASTION_CREDENTIALS = 'jenkins-user'
    BASTION_HOST = '**************'
    BASTION_USER = 'jenkins'

    // => Add at customer folder level
    DEPLOY_SERVER_CREDENTIALS = 'deploy-server-ssh-credentials'
    DEPLOY_SERVER_HOST = '*************'
    DEPLOY_SERVER_USER = 'ubuntu'

    // AWS credentials for ECR => Add at project folder level (Shared for all customers)
    AWS_ACCESS_KEY_ID = credentials('aws-access-key-id')
    AWS_SECRET_ACCESS_KEY = credentials('aws-secret-access-key')
    AWS_DEFAULT_REGION = 'us-east-1'

    // Deployment configuration - these variables can be overridden for each customer
    // Store these in customer-specific credentials or variables
    APP_DIRECTORY = '/etc/easypanel/projects/cagent/cagent/code'
    WEBHOOK_URL = 'http://*************:3000/api/compose/deploy/1a2760896d086ed780a2ff0e037fd63107a382216e10cc0a'
  }

  stages {
    stage('Prepare Build') {
      steps {
        script {
          def tag = sh(
            script: "git rev-parse --short HEAD",
            returnStdout: true
          ).trim()
          // Ensure tag is valid for Docker (replace '/' with '-')
          env.COMMIT_TAG = tag.replaceAll('/', '-')

          // Create customer-specific tag
          env.CUSTOMER_TAG = "${env.CUSTOMER_NAME}-${env.COMMIT_TAG}"

          echo "✅ Using CUSTOMER_NAME=${env.CUSTOMER_NAME}"
          echo "✅ Using COMMIT_TAG=${env.COMMIT_TAG}"
          echo "✅ Using CUSTOMER_TAG=${env.CUSTOMER_TAG}"
          echo "✅ Using BASE_IMAGE=${env.BASE_IMAGE} for caching"
        }
      }
    }

    stage('Check & Pull Base Image') {
      steps {
        script {
          def baseImageExists = sh(
            script: "docker images -q ${BASE_IMAGE} | wc -l",
            returnStdout: true
          ).trim()

          if (baseImageExists == "0") {
            echo "🔄 Base image does not exist locally, pulling..."
            sh "docker pull ${BASE_IMAGE} || echo 'Failed to pull base image, build may be slower'"
          } else {
            echo "✅ Base image exists locally, using for cache"
          }
        }
      }
    }

    stage('Build & Push to CMC Registry') {
      steps {
        sh '''
          echo "🔨 Building customer image with base image caching..."

          # Build customer image using cache from base image
          docker build --cache-from ${BASE_IMAGE} \
                      --build-arg CUSTOMER=${CUSTOMER_NAME} \
                      -t ${CMC_REPOSITORY}:${CUSTOMER_TAG} \
                      .

          # Push image with commit ID tag to CMC Registry
          echo "🚀 Pushing image to CMC Registry..."
          docker push ${CMC_REPOSITORY}:${CUSTOMER_TAG}

          echo "✅ Pushed image to CMC Registry: ${CMC_REPOSITORY}:${CUSTOMER_TAG}"
        '''
      }
    }

    stage('Transfer to ECR via Bastion') {
      steps {
        script {
          // Use double quotes to allow variable expansion
          writeFile file: 'transfer_image.sh', text: """#!/bin/bash
          set -e

          # Pull from CMC registry
          docker pull ${env.CMC_PUBLIC_REGISTRY}/${env.DOCKER_IMAGE}:${env.CUSTOMER_TAG}

          # Login to ECR
          aws ecr get-login-password \\
            --region ${env.AWS_DEFAULT_REGION} | docker login \\
            --username AWS --password-stdin ${env.ECR_REGISTRY}

          # Retag 2 images: commit and latest
          docker tag ${env.CMC_PUBLIC_REGISTRY}/${env.DOCKER_IMAGE}:${env.CUSTOMER_TAG} ${env.ECR_REPOSITORY}:${env.CUSTOMER_TAG}
          docker tag ${env.CMC_PUBLIC_REGISTRY}/${env.DOCKER_IMAGE}:${env.CUSTOMER_TAG} ${env.ECR_REPOSITORY}:latest

          # Push both tags
          docker push ${env.ECR_REPOSITORY}:${env.CUSTOMER_TAG}
          docker push ${env.ECR_REPOSITORY}:latest

          # Cleanup
          docker rmi ${env.CMC_PUBLIC_REGISTRY}/${env.DOCKER_IMAGE}:${env.CUSTOMER_TAG} \\
                    ${env.ECR_REPOSITORY}:${env.CUSTOMER_TAG} \\
                    ${env.ECR_REPOSITORY}:latest || true
          """

          // Also use double quotes for the sh commands
          sshagent(credentials: [env.BASTION_CREDENTIALS]) {
            sh """
              chmod +x transfer_image.sh
              scp -o StrictHostKeyChecking=no transfer_image.sh \\
                  ${env.BASTION_USER}@${env.BASTION_HOST}:/tmp/
              ssh -o StrictHostKeyChecking=no ${env.BASTION_USER}@${env.BASTION_HOST} \\
                  "bash /tmp/transfer_image.sh"
              ssh -o StrictHostKeyChecking=no ${env.BASTION_USER}@${env.BASTION_HOST} \\
                  "rm -f /tmp/transfer_image.sh"
            """
          }
          sh "rm -f transfer_image.sh"
        }
      }
    }

    stage('Deploy to Server') {
      steps {
        sshagent(credentials: [env.DEPLOY_SERVER_CREDENTIALS]) {
          sh '''
            echo "🔍 Detecting EasyPanel container…"
            EASYPANEL_CONTAINER=$(
              ssh -o StrictHostKeyChecking=no ${DEPLOY_SERVER_USER}@${DEPLOY_SERVER_HOST} \
                "sudo docker ps --filter ancestor=easypanel/easypanel:latest --format '{{.Names}}' | head -n1"
            )
            if [ -z "$EASYPANEL_CONTAINER" ]; then
              echo "❌ EasyPanel container not found!"
              exit 1
            fi
            echo "➡️ Found container: $EASYPANEL_CONTAINER"

            echo "🔐 Logging into AWS ECR inside container…"
            ssh -o StrictHostKeyChecking=no ${DEPLOY_SERVER_USER}@${DEPLOY_SERVER_HOST} \
              "sudo docker exec -i $EASYPANEL_CONTAINER \
                aws ecr get-login-password --region ${AWS_DEFAULT_REGION} \
              | sudo docker exec -i $EASYPANEL_CONTAINER \
                docker login --username AWS --password-stdin ${ECR_REGISTRY}"

            echo "✅ ECR login succeeded"
            echo "🚀 Triggering deployment webhook…"
            curl -X POST ${WEBHOOK_URL} || true
          '''
        }
      }
    }




    stage('Cleanup Jenkins Server') {
      steps {
        script {
          echo "🧹 Cleaning up customer images from Jenkins server..."

          // Remove customer image with specific tag
          sh "docker rmi ${CMC_REPOSITORY}:${CUSTOMER_TAG} || true"

          echo "✅ Cleaned up customer images from Jenkins server"

          // Show remaining images
          sh "echo 'Remaining images on Jenkins server:'"
          sh "docker images | grep -E '${CMC_REPOSITORY}|${BASE_IMAGE}' || echo 'No related images found'"
        }
      }
    }
  }

  post {
    success {
      echo "✅ Pipeline completed successfully: Image has been built, pushed, and deployed"
    }
    failure {
      echo "❌ Pipeline failed!"
    }
    always {
      // Clean up workspace
      cleanWs()
    }
  }
}
