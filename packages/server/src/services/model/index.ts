import { BedrockRuntimeClient, InvokeModelCommand } from '@aws-sdk/client-bedrock-runtime'

// Initialize AWS Bedrock client for embedding generation
const bedrockClient = new BedrockRuntimeClient({
  region: process.env.S3_STORAGE_REGION || 'us-east-1'
})

const taskDescription = `
## NHIỆM VỤ

Phân tích cuộc hội thoại để trích xuất cặp câu hỏi-trả lời và cập nhật danh sách QA hiện có.

## ĐẦU VÀO

* **qa_list**: Danh sách các cặp câu hỏi-trả lời hiện có, mỗi mục bao gồm:
  - id: mã định danh duy nhất (giữ nguyên)
  - question: nội dung câu hỏi
  - answer: nội dung câu trả lời
  - time: thời gian tạo (giữ nguyên)

* **chat_history**: <PERSON><PERSON><PERSON><PERSON> hội thoại mới giữa khách hàng và tư vấn viên, mỗi mục bao gồm:
  - question: nội dung câu hỏi
  - answer: nội dung câu trả lời
  - time: thời gian tạo (giữ nguyên)

## ĐẦU RA

Trả về một mảng JSON với cấu trúc:
[
  {
    "id": "...",
    "question": "...",
    "answer": "...",
    "time": "..."
  }
]

Yêu cầu:
1. Giữ lại tất cả mục trong qa_list ban đầu
2. Nếu chat_history bổ sung thông tin cho câu hỏi cuối cùng trong qa_list, cập nhật trường answer
3. Thêm các cặp QA mới được phát hiện trong chat_history với id mới

## QUY TẮC XỬ LÝ

1. Xác định câu hỏi (kể cả ngầm định) và ghép với câu trả lời đầy đủ
2. Nếu chat_history mở rộng câu hỏi cuối cùng của qa_list, nối thông tin mới vào answer
3. Giữ thứ tự: các mục cũ (đã cập nhật) trước, các mục mới sau theo thứ tự xuất hiện
4. Đầu ra phải là mảng JSON trực tiếp, không có giải thích

## VÍ DỤ

**qa_list ban đầu:**
[
  {
    "id": "1",
    "question": "Lãi suất vay mua nhà hiện tại là bao nhiêu?",
    "answer": "Từ 8.5% đến 10.5% tùy gói và thời hạn.",
    "time": "2023-06-15T10:15:22Z"
  }
]

**chat_history:**
Khách: Với lãi suất vay mua nhà, VIB có ưu đãi nào không?
Agent: Hiện có gói 7.9% cố định 12 tháng (4–6/2023).
time: "2023-06-15T10:15:22Z"
Khách: Tôi muốn biết về bảo hiểm khoản vay.
Agent: Yêu cầu bảo hiểm bắt buộc, phí 0.5–1.5% giá trị khoản vay.
time: "2023-06-15T10:15:22Z"
**Kết quả mong đợi:**
[
  {
    "id": "1",
    "question": "Lãi suất vay mua nhà hiện tại là bao nhiêu?",
    "answer": "Từ 8.5% đến 10.5% tùy gói và thời hạn. Hiện có gói 7.9% cố định 12 tháng (4–6/2023).",
    "time": "2023-06-15T10:15:22Z"
  },
  {
    "id": "2",
    "question": "Tôi muốn biết về bảo hiểm khoản vay.",
    "answer": "Yêu cầu bảo hiểm bắt buộc, phí 0.5–1.5% giá trị khoản vay.",
    "time": "2023-06-16T14:25:33Z"
  }
]
## ĐẦU VÀO:
Đoạn hội thoại:
{chat_history}
Danh sách QA đã có:
{qa_list}
`

export interface QAItem {
  question: string
  answer: string
  time: Date
}

export interface ChatHistory {
  id: string
  answer: string
  question: string
  time: Date
}

export const analyze = async (chat_history: ChatHistory[], qa_list: QAItem[]): Promise<string> => {
  try {
    const formattedPrompt = `${taskDescription
      .replace('{chat_history}', JSON.stringify(chat_history))
      .replace('{qa_list}', JSON.stringify(qa_list))}`

    const command = new InvokeModelCommand({
      modelId: 'us.anthropic.claude-3-7-sonnet-20250219-v1:0',
      contentType: 'application/json',
      accept: 'application/json',
      body: JSON.stringify({
        anthropic_version: 'bedrock-2023-05-31',
        max_tokens: 100000,
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: formattedPrompt
              }
            ]
          }
        ]
      })
    })
    const response = await bedrockClient.send(command)
    const responseBody = JSON.parse(new TextDecoder().decode(response.body))
    return responseBody.content[0].text as string
  } catch (error) {
    console.log('🚀 ~ index.ts:179 ~ analyze ~ error:', error)

    throw error
  }
}

const taskDescription2 = `
## NHIỆM VỤ
Phân tích đoạn hội thoại, phân loại các câu hỏi theo nhãn đã cho hoặc tự định nghĩa.

## ĐẦU VÀO
1. Đoạn hội thoại (qa_list): Danh sách cặp câu hỏi - trả lời.
2. Nhãn câu hỏi (labels): Danh sách nhãn để phân loại câu hỏi (có thể có hoặc không).

## YÊU CẦU ĐẦU RA
Trả về mảng JSON các câu hỏi đã được phân loại:
- "question": Câu hỏi được hỏi.
- "label": Loại câu hỏi theo nhãn đã cung cấp hoặc tự định nghĩa.

## QUY TẮC PHÂN LOẠI
1. Nếu có "labels": phân loại câu hỏi vào đúng nhãn. Không tạo thêm nhãn mới.
2. Nếu không có "labels":
   - Xác định chủ đề chính của hội thoại.
   - Tạo nhãn phù hợp với chủ đề chính.
   - Câu hỏi lạc chủ đề gán nhãn "Khác".

## LƯU Ý
- Đảm bảo đầy đủ các câu hỏi theo thứ tự ban đầu.
- Nếu không phân loại được, gán "Khác".
- Đầu ra phải là mảng JSON trực tiếp, bỏ qua giải thích và câu diễn giải.

## ĐẦU VÀO:
Đoạn hội thoại:
{qa_list}

Danh sách nhãn câu hỏi (nếu có):
{labels}
`

export const classifyQuestions = async (qa_list: { question: string; answer: string }[], labels: string[]): Promise<string> => {
  try {
    const formattedPrompt = `${taskDescription2.replace('{qa_list}', JSON.stringify(qa_list)).replace('{labels}', JSON.stringify(labels))}`

    const command = new InvokeModelCommand({
      modelId: 'us.anthropic.claude-3-7-sonnet-20250219-v1:0',
      contentType: 'application/json',
      accept: 'application/json',
      body: JSON.stringify({
        anthropic_version: 'bedrock-2023-05-31',
        max_tokens: 100000,
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: formattedPrompt
              }
            ]
          }
        ]
      })
    })
    const response = await bedrockClient.send(command)
    const responseBody = JSON.parse(new TextDecoder().decode(response.body))
    return responseBody.content[0].text as string
  } catch (error) {
    console.log('🚀 ~ index.ts:179 ~ classifyQuestions ~ error:', error)

    throw error
  }
}

export const generateSystemPromptModel = async (promptDescription: string): Promise<string> => {
  try {
    const command = new InvokeModelCommand({
      modelId: 'us.anthropic.claude-3-7-sonnet-20250219-v1:0',
      contentType: 'application/json',
      accept: 'application/json',
      body: JSON.stringify({
        anthropic_version: 'bedrock-2023-05-31',
        max_tokens: 4000,
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: `Bạn là một chuyên gia tạo prompt (prompt engineer) chuyên nghiệp. Nhiệm vụ của bạn là tạo ra một prompt mới dựa trên template có sẵn nhưng được tùy chỉnh theo yêu cầu đầu vào cụ thể.

Template prompt:
<template/>
#Bạn là trợ lý ảo của ..., nhiệm vụ của bạn là ...

# Các bước xử lý:
## Bước 1: ...
## Bước 2: ...
## Bước 3: ...

# Quy tắc trả lời và các lưu ý:
- Yêu cầu 1
- Yêu cầu 2
- Yêu cầu 3
...
</template>

## Hãy tạo một prompt mới tuân thủ các nguyên tắc sau:
1. Giữ nguyên cấu trúc và format của template đã cung cấp
2. Thay đổi nội dung, tùy chỉnh ngôn ngữ và hướng dẫn cụ thể để phù hợp với mô tả và yêu cầu của người dùng
3. Đảm bảo rằng tất cả các thành phần quan trọng trong template gốc vẫn được giữ lại
4. Nên thêm bước sau vào phần đầu của mục "các bước xử lý", nêu nghiệp vụ người dùng không yêu cầu, có thể bỏ qua bước này:
 - Chia nhỏ câu hỏi của người dùng thành các câu hỏi con (sub-queries).
 - Sử dụng công cụ tra cứu ứng với từng câu hỏi con.
5. Bước cuối cùng của "Các bước xử lý" nên là "Tổng hợp thông tin và trả lời câu hỏi".
6. Vui lòng trình bày prompt mới hoàn chỉnh mà không bỏ sót bất kỳ phần nào của template gốc, không thêm các câu diễn giải, đầu ra phải là một prompt hoàn chỉnh có thể sử dụng.

Mô tả của người dùng:
${promptDescription}`
              }
            ]
          }
        ]
      })
    })
    const response = await bedrockClient.send(command)
    return JSON.parse(new TextDecoder().decode(response.body)).content[0].text
  } catch (error) {
    console.error('Bedrock generation error:', error)
    throw new Error('Failed to generate prompt')
  }
}
