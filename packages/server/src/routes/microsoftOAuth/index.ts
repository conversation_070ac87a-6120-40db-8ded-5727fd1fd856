import express, { Router } from 'express'
import microsoftOAuthController from '../../controllers/microsoftOAuth'

const router: Router = express.Router()

// Test endpoint to check if backend is working
router.get('/test', (req, res) => {
  console.log('🔐 [Microsoft OAuth]: Test endpoint called')
  res.json({ message: 'Microsoft OAuth backend is working', timestamp: new Date().toISOString() })
})

// Microsoft OAuth routes
router.post('/login', microsoftOAuthController.loginWithMicrosoft)
router.post('/exchange', microsoftOAuthController.exchangeCode)

export default router
