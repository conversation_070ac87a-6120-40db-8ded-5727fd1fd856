import express, { Router } from 'express'
import { getChatHistory } from '../../controllers/dashboard'
import { getCustomerFeedback } from '../../controllers/dashboard/customerFeedback'
import { unansweredQuestions } from '../../controllers/dashboard/unansweredQuestions'
import { questionsOverTime } from '../../controllers/dashboard/questionsOverTime'

const router: Router = express.Router()

router.get('/chat-history', getChatHistory)
router.get('/customer-feedback', getCustomerFeedback)
router.get('/unanswered-questions', unansweredQuestions)
router.get('/questions-over-time', questionsOverTime)

export default router
