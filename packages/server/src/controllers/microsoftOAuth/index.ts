import { Request, Response, NextFunction } from 'express'
import { StatusCodes } from 'http-status-codes'
import { InternalFlowiseError } from '../../errors/internalFlowiseError'
import microsoftOAuthService from '../../services/microsoftOAuth'
import userService from '../../services/user'

// Exchange authorization code for access token
const exchangeCode = async (req: Request, res: Response, next: NextFunction) => {
  try {
    console.log('🔐 [Microsoft OAuth Controller]: Received code exchange request')

    const { code, redirectUri } = req.body

    if (!code) {
      console.error('🔐 [Microsoft OAuth Controller]: Missing authorization code in request')
      throw new InternalFlowiseError(StatusCodes.BAD_REQUEST, 'Authorization code is required')
    }

    if (!redirectUri) {
      console.error('🔐 [Microsoft OAuth Controller]: Missing redirect URI in request')
      throw new InternalFlowiseError(StatusCodes.BAD_REQUEST, 'Redirect URI is required')
    }

    console.log('🔐 [Microsoft OAuth Controller]: Processing code exchange', {
      codeLength: code.length,
      codePreview: code.substring(0, 20) + '...',
      redirectUri
    })

    const result = await microsoftOAuthService.exchangeCode(code, redirectUri)

    console.log('🔐 [Microsoft OAuth Controller]: Code exchange successful', {
      hasAccessToken: !!result.accessToken,
      tokenLength: result.accessToken?.length,
      hasUserInfo: !!result.userInfo
    })

    return res.status(StatusCodes.OK).json({
      accessToken: result.accessToken,
      userInfo: result.userInfo
    })
  } catch (error: any) {
    console.error('🔐 [Microsoft OAuth Controller]: Code exchange failed', {
      error: error.message,
      statusCode: error.statusCode
    })
    next(error)
  }
}

// Login with Microsoft OAuth
const loginWithMicrosoft = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { accessToken } = req.body

    if (!accessToken) {
      console.error('🔐 [Microsoft OAuth Controller]: Missing access token in request')
      throw new InternalFlowiseError(StatusCodes.BAD_REQUEST, 'Access token is required')
    }

    const result = await microsoftOAuthService.loginWithMicrosoft(accessToken)

    return res.status(StatusCodes.OK).json(result)
  } catch (error: any) {
    console.error('🔐 [Microsoft OAuth Controller]: Microsoft OAuth login failed', {
      error: error.message,
      statusCode: error.statusCode
    })
    next(error)
  }
}

export default {
  loginWithMicrosoft,
  exchangeCode
}
