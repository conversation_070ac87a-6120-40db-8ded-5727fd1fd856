import dayjs from 'dayjs'
import { NextFunction, Request, Response } from 'express'
import { map } from 'lodash'
import { getRunningExpressApp } from '../../utils/getRunningExpressApp'
import { QA } from '../../database/entities/AQ'

type Item = {
  label: string
  value: number
}

export const questionsOverTime = async (req: Request, res: Response, next: NextFunction) => {
  let { flowId } = req.query

  const chatflowid = flowId as string

  if (!chatflowid) {
    return res.status(400).json({ error: 'flowId is required' })
  }

  const appServer = getRunningExpressApp()
  const chatMessageRepo = appServer.AppDataSource.getRepository(QA)

  const { fromDate, toDate } = req.query as {
    fromDate: string
    toDate: string
  }

  let items: Record<string, Item> = {}

  // Parse the ISO string dates and adjust to UTC+7
  const startDateUtcPlus7 = dayjs(fromDate)
  const endDateUtcPlus7 = dayjs(toDate)

  let currentDate = startDateUtcPlus7.add(1, 'day')

  while (currentDate.isBefore(endDateUtcPlus7) || currentDate.isSame(endDateUtcPlus7, 'day')) {
    const dayStart = currentDate.startOf('day')
    const dayEnd = currentDate.add(1, 'day').startOf('day')

    try {
      const chatMessagesCount = await chatMessageRepo
        .createQueryBuilder('chatMessage')
        .where('chatMessage.chatflowid = :chatflowid', { chatflowid })
        .andWhere('chatMessage.createdDate BETWEEN :fromDate AND :toDate', {
          fromDate: dayStart.subtract(7, 'hour').toISOString(),
          toDate: dayEnd.subtract(7, 'hour').toISOString()
        })
        .getCount()

      const label = dayEnd.subtract(7, 'hour').format('YYYY-MM-DD')
      items[label] = {
        label,
        value: chatMessagesCount
      }
    } catch (error) {
      return next(error)
    }

    // Move to next day
    currentDate = currentDate.add(1, 'day')
  }

  res.json({
    data: map(items, (item) => item)
  })
}
