import { NextFunction, Request, Response } from 'express'
import { getRunningExpressApp } from '../../utils/getRunningExpressApp'
import { ChatMessageFeedback } from '../../database/entities/ChatMessageFeedback'
import { ChatMessageRatingType } from '../../Interface'

export const getCustomerFeedback = async (req: Request, res: Response, next: NextFunction) => {
  let { type, page = '1', limit = '10', flowId } = req.query
  let rating: any
  if (!type) {
    rating = undefined
  } else {
    rating = type === 'positive' ? ChatMessageRatingType.THUMBS_UP : ChatMessageRatingType.THUMBS_DOWN
  }

  const chatflowid = flowId as string

  // Filter by type if specified
  const appServer = getRunningExpressApp()
  const chatMessageFeedback = appServer.AppDataSource.getRepository(ChatMessageFeedback)

  const allItems = await chatMessageFeedback.find({
    where: {
      ...(rating ? { rating } : {}),
      chatflowid: chatflowid
    },
    order: {
      createdDate: 'DESC'
    }
  })

  const filteredItems = allItems.map((item) => ({
    id: item.id,
    content: item.content,
    date: item.createdDate,
    messageId: item.messageId,
    type: item.rating === ChatMessageRatingType.THUMBS_UP ? 'positive' : 'negative',
    question: item.question,
    answer: item.answer
  }))

  // Pagination
  const pageNum = parseInt(page as string)
  const limitNum = parseInt(limit as string)
  const startIndex = (pageNum - 1) * limitNum
  const endIndex = startIndex + limitNum
  const paginatedItems = filteredItems.slice(startIndex, endIndex)

  // Count totals
  const count = filteredItems.length

  // Count totals
  const negativeCount = filteredItems.filter((item) => item.type === 'negative').length
  const positiveCount = filteredItems.filter((item) => item.type === 'positive').length

  res.json({
    negative: negativeCount,
    positive: positiveCount,
    totalFeedback: count,
    items: paginatedItems,
    page: pageNum,
    limit: limitNum,
    total: filteredItems.length
  })
}
