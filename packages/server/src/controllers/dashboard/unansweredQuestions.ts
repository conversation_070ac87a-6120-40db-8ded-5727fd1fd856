import { NextFunction, Request, Response } from 'express'
import { getRunningExpressApp } from '../../utils/getRunningExpressApp'
import { QA } from '../../database/entities/AQ'

export const unansweredQuestions = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const appServer = getRunningExpressApp()
    const chatMessageRepo = appServer.AppDataSource.getRepository(QA)
    const flowId = req.query.flowId as string

    // Fetch all records from the QA table
    const allRecords = await chatMessageRepo.find({ where: { chatflowid: flowId, being_processed: false } })

    // Calculate total number of records
    const totalRecords = allRecords.length

    // Group records by question_type and calculate totals
    const groupedData = allRecords.reduce((acc, record) => {
      const category = record.question_type || 'Khác'
      if (!acc[category]) {
        acc[category] = { category, total: 0 }
      }
      acc[category].total += 1
      return acc
    }, {} as Record<string, { category: string; total: number }>)

    // Transform grouped data into the desired format with percentages
    const responseData = Object.values(groupedData).map((item) => ({
      category: item.category,
      total: item.total,
      percent: parseFloat(((item.total / totalRecords) * 100).toFixed(1))
    }))

    // Send the response
    res.json({ data: responseData })
  } catch (error) {
    next(error)
  }
}
