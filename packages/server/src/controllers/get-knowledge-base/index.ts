import { Request, Response, NextFunction } from 'express'
import { KnowledgeBase } from '../../database/entities/IngestionJobs'
import { getRunningExpressApp } from '../../utils/getRunningExpressApp'

const getFolderNameByKB = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { knowledgeBaseID } = req.body
    if (!knowledgeBaseID) {
      console.error('knowledgeBaseID is empty')
    }
    const appServer = getRunningExpressApp()
    const response = await appServer.AppDataSource.getRepository(KnowledgeBase).findOne({
      where: {
        knowledge_base_id: knowledgeBaseID
      }
    })
    if (response) {
      res.status(200).json({
        name: response?.folder_name
      })
    } else {
      res.status(404).json({
        message: 'Knowledge base not found'
      })
    }
  } catch (error) {
    console.log(error)
  }
}

const getAllKnowledgeBases = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const appServer = getRunningExpressApp()
    const knowledgeBases = await appServer.AppDataSource.getRepository(KnowledgeBase).find({
      relations: ['chatflows'],
      select: {
        chatflows: {
          id: true,
          name: true,
          createdDate: true,
          type: true
        }
      }
    })

    if (knowledgeBases) {
      res.status(200).json({
        knowledgeBases
      })
    } else {
      res.status(404).json({
        message: 'No knowledge bases found'
      })
    }
  } catch (error) {
    console.error('Error fetching knowledge bases:', error)
    res.status(500).json({
      message: 'Failed to retrieve knowledge bases'
    })
  }
}

const getKnowledgeBaseById = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params

    if (!id) {
      return res.status(400).json({
        message: 'Knowledge base ID is required'
      })
    }

    const appServer = getRunningExpressApp()
    const knowledgeBase = await appServer.AppDataSource.getRepository(KnowledgeBase).findOne({
      where: {
        id: id
      },
      relations: ['chatflows'],
      select: {
        chatflows: {
          id: true,
          name: true,
          createdDate: true,
          type: true
        }
      }
    })

    if (knowledgeBase) {
      res.status(200).json({
        knowledgeBase
      })
    } else {
      res.status(404).json({
        message: 'Knowledge base not found'
      })
    }
  } catch (error) {
    console.error('Error fetching knowledge base:', error)
    res.status(500).json({
      message: 'Failed to retrieve knowledge base'
    })
  }
}

const getKnowledgeBaseByBaseId = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { knowledgeBaseID } = req.params

    if (!knowledgeBaseID) {
      return res.status(400).json({
        message: 'Knowledge base ID is required'
      })
    }

    const appServer = getRunningExpressApp()
    const knowledgeBase = await appServer.AppDataSource.getRepository(KnowledgeBase).findOne({
      where: {
        knowledge_base_id: knowledgeBaseID
      },
      relations: ['chatflows'],
      select: {
        chatflows: {
          id: true,
          name: true,
          createdDate: true,
          type: true
        }
      }
    })

    if (knowledgeBase) {
      res.status(200).json({
        knowledgeBase
      })
    } else {
      res.status(404).json({
        message: 'Knowledge base not found'
      })
    }
  } catch (error) {
    console.error('Error fetching knowledge base:', error)
    res.status(500).json({
      message: 'Failed to retrieve knowledge base'
    })
  }
}

const getKnowledgeBaseByFolderName = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { folderName } = req.params

    if (!folderName) {
      return res.status(400).json({
        message: 'Folder name is required'
      })
    }

    const appServer = getRunningExpressApp()
    const knowledgeBase = await appServer.AppDataSource.getRepository(KnowledgeBase).findOne({
      where: {
        folder_name: folderName + '/'
      },
      relations: ['chatflows'],
      select: {
        chatflows: {
          id: true,
          name: true,
          createdDate: true,
          type: true
        }
      }
    })

    if (knowledgeBase) {
      res.status(200).json({
        knowledgeBase
      })
    } else {
      res.status(404).json({
        message: 'Knowledge base not found'
      })
    }
  } catch (error) {
    console.error('Error fetching knowledge base by folder name:', error)
    res.status(500).json({
      message: 'Failed to retrieve knowledge base'
    })
  }
}

export default {
  getFolderNameByKB,
  getAllKnowledgeBases,
  getKnowledgeBaseById,
  getKnowledgeBaseByBaseId,
  getKnowledgeBaseByFolderName
}
