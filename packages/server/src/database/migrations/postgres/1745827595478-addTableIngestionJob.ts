import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddTableIngestionJob1745827595478 implements MigrationInterface {
  name = 'AddTableIngestionJob1745827595478'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "ingestion_jobs_status" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "ingestion_job_id" character varying, "knowledgeBaseId" character varying, "dataSourceId" character varying, "user_id" uuid, "userName" character varying, "status" character varying DEFAULT 'COMPLETE', "isStillInStock" boolean NOT NULL DEFAULT false, "created_date" TIMESTAMP NOT NULL DEFAULT now(), "updated_date" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_a175f1a00983bdb776cba1e3c6e" PRIMARY KEY ("id"))`
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "ingestion_jobs_status"`)
  }
}
