import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddTableQA1744260983643 implements MigrationInterface {
  name = 'AddTableQA1744260983643'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "qa" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "question" character varying NOT NULL, "answer" text NOT NULL, "chatId" uuid, "chatflowid" uuid NOT NULL, "timeLastCron" TIMESTAMP NOT NULL DEFAULT now(), "createdDate" TIMESTAMP NOT NULL DEFAULT now(), "updatedDate" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_2e65f4d391220e938dd4d03975f" PRIMARY KEY ("id"))`
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "qa"`)
  }
}
