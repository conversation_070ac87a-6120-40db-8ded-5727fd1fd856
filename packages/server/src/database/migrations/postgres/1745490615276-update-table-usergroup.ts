import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateTableUsergroup1745490615276 implements MigrationInterface {
  name = 'UpdateTableUsergroup1745490615276'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "group_users" ADD "parentGroupId" uuid`)
    await queryRunner.query(
      `ALTER TABLE "group_users" ADD CONSTRAINT "FK_87d864caf528617857c8697f725" FOREIGN KEY ("parentGroupId") REFERENCES "group_users"("id") ON DELETE SET NULL ON UPDATE NO ACTION`
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "group_users" DROP CONSTRAINT "FK_87d864caf528617857c8697f725"`)
    await queryRunner.query(`ALTER TABLE "group_users" DROP COLUMN "parentGroupId"`)
  }
}
