import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddShowDashboardFlow1745569176138 implements MigrationInterface {
  name = 'AddShowDashboardFlow1745569176138'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "chat_flow" ADD "showDashboard" boolean`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "chat_flow" DROP COLUMN "showDashboard"`)
  }
}
