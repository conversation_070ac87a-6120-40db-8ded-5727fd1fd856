import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddTableLabel1745565382242 implements MigrationInterface {
  name = 'AddTableLabel1745565382242'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "label" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "label" text NOT NULL, "count" bigint NOT NULL DEFAULT '0', "chatflowid" uuid NOT NULL, "createdDate" TIMESTAMP NOT NULL DEFAULT now(), "updatedDate" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_5692ac5348861d3776eb5843672" PRIMARY KEY ("id"))`
    )
    const hasActiveColumn = await queryRunner.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'users' AND column_name = 'active'
    `)
    if (hasActiveColumn.length === 0) {
      await queryRunner.query(`ALTER TABLE "users" ADD "active" boolean DEFAULT true`)
    }
    await queryRunner.query(`ALTER TYPE "public"."users_role_enum" RENAME TO "users_role_enum_old"`)
    await queryRunner.query(`CREATE TYPE "public"."users_role_enum" AS ENUM('MASTER_ADMIN', 'SITE_ADMIN', 'ADMIN', 'USER')`)
    await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "role" DROP DEFAULT`)
    await queryRunner.query(
      `ALTER TABLE "users" ALTER COLUMN "role" TYPE "public"."users_role_enum" USING "role"::"text"::"public"."users_role_enum"`
    )
    await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "role" SET DEFAULT 'USER'`)
    await queryRunner.query(`DROP TYPE "public"."users_role_enum_old"`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TYPE "public"."users_role_enum_old" AS ENUM('MASTER_ADMIN', 'ADMIN', 'USER')`)
    await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "role" DROP DEFAULT`)
    await queryRunner.query(
      `ALTER TABLE "users" ALTER COLUMN "role" TYPE "public"."users_role_enum_old" USING "role"::"text"::"public"."users_role_enum_old"`
    )
    await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "role" SET DEFAULT 'USER'`)
    await queryRunner.query(`DROP TYPE "public"."users_role_enum"`)
    await queryRunner.query(`ALTER TYPE "public"."users_role_enum_old" RENAME TO "users_role_enum"`)
    await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "active"`)
    await queryRunner.query(`DROP TABLE "label"`)
  }
}
