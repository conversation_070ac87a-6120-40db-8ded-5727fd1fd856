import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateFeebbackMessage1744598612079 implements MigrationInterface {
  name = 'UpdateFeebbackMessage1744598612079'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "chat_message_feedback" ADD "question" text`)
    await queryRunner.query(`ALTER TABLE "chat_message_feedback" ADD "answer" text`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "chat_message_feedback" DROP COLUMN "answer"`)
    await queryRunner.query(`ALTER TABLE "chat_message_feedback" DROP COLUMN "question"`)
  }
}
