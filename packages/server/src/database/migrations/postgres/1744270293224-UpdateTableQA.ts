import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateTableQA1744270293224 implements MigrationInterface {
  name = 'UpdateTableQA1744270293224'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "qa" ADD "question_type" text NOT NULL`)
    await queryRunner.query(`ALTER TABLE "qa" ADD "being_processed" boolean`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "qa" DROP COLUMN "being_processed"`)
    await queryRunner.query(`ALTER TABLE "qa" DROP COLUMN "question_type"`)
  }
}
