import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateTableQATableAddUserId1744367625387 implements MigrationInterface {
  name = 'UpdateTableQATableAddUserId1744367625387'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "qa" ADD "userId" uuid`)
    await queryRunner.query(`ALTER TABLE "qa" ADD "userName" text`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "qa" DROP COLUMN "userName"`)
    await queryRunner.query(`ALTER TABLE "qa" DROP COLUMN "userId"`)
  }
}
