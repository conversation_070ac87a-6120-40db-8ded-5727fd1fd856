import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateTableIngestionJobsStatus1745897313368 implements MigrationInterface {
  name = 'UpdateTableIngestionJobsStatus1745897313368'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "ingestion_jobs_status" DROP COLUMN "isStillInStock"`)
    await queryRunner.query(`ALTER TABLE "ingestion_jobs_status" DROP COLUMN "dataSourceId"`)
    await queryRunner.query(`ALTER TABLE "ingestion_jobs_status" DROP COLUMN "userName"`)
    await queryRunner.query(`ALTER TABLE "ingestion_jobs_status" DROP COLUMN "knowledgeBaseId"`)
    await queryRunner.query(`ALTER TABLE "ingestion_jobs_status" ADD "knowledge_base_id" character varying`)
    await queryRunner.query(`ALTER TABLE "ingestion_jobs_status" ADD "data_source_id" character varying`)
    await queryRunner.query(`ALTER TABLE "ingestion_jobs_status" ADD "user_name" character varying`)
    await queryRunner.query(`ALTER TABLE "ingestion_jobs_status" ADD "is_still_in_stock" boolean NOT NULL DEFAULT false`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "ingestion_jobs_status" DROP COLUMN "is_still_in_stock"`)
    await queryRunner.query(`ALTER TABLE "ingestion_jobs_status" DROP COLUMN "user_name"`)
    await queryRunner.query(`ALTER TABLE "ingestion_jobs_status" DROP COLUMN "data_source_id"`)
    await queryRunner.query(`ALTER TABLE "ingestion_jobs_status" DROP COLUMN "knowledge_base_id"`)
    await queryRunner.query(`ALTER TABLE "ingestion_jobs_status" ADD "knowledgeBaseId" character varying`)
    await queryRunner.query(`ALTER TABLE "ingestion_jobs_status" ADD "userName" character varying`)
    await queryRunner.query(`ALTER TABLE "ingestion_jobs_status" ADD "dataSourceId" character varying`)
    await queryRunner.query(`ALTER TABLE "ingestion_jobs_status" ADD "isStillInStock" boolean NOT NULL DEFAULT false`)
  }
}
