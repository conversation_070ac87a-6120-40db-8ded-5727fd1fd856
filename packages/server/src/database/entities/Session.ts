/* eslint-disable */
import { <PERSON><PERSON><PERSON>, Column, CreateDateColumn, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm'
import { ISession } from '../../Interface'

@Entity()
export class Session implements ISession {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({ type: 'uuid' })
  chatflowid: string

  @Column({ type: 'text', nullable: true })
  summary: string

  @Column({ type: 'varchar', nullable: true })
  sessionId?: string

  @Column({ type: 'timestamp' })
  @CreateDateColumn()
  createdDate: Date

  @Column({ type: 'timestamp' })
  @UpdateDateColumn()
  updatedDate: Date
}
