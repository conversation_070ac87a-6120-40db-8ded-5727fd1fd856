import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm'

@Entity('ingestion_jobs_status')
export class ingestionJobsStatus {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({ type: 'varchar', nullable: true })
  ingestion_job_id: string

  @Column({ type: 'varchar', nullable: true })
  knowledge_base_id: string

  @Column({ type: 'varchar', nullable: true })
  data_source_id: string

  @Column({ type: 'uuid', nullable: true })
  user_id: string

  @Column({ type: 'varchar', nullable: true })
  user_name: string

  @Column({ type: 'varchar', nullable: true, default: 'COMPLETE' })
  status: string

  @Column({ type: 'boolean', default: false })
  is_still_in_stock: boolean

  @CreateDateColumn({ type: 'timestamp' })
  created_date: Date

  @UpdateDateColumn({ type: 'timestamp' })
  updated_date: Date
}
