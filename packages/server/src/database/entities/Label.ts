import { En<PERSON>ty, Column, CreateDateColumn, UpdateDateColumn, PrimaryGeneratedColumn } from 'typeorm'

@Entity()
export class Label {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({ type: 'text' })
  label: string

  @Column({ type: 'bigint', default: 0 })
  count: number

  @Column({ type: 'uuid' })
  chatflowid: string

  @Column({ type: 'timestamp' })
  @CreateDateColumn()
  createdDate: Date

  @Column({ type: 'timestamp' })
  @UpdateDateColumn()
  updatedDate: Date
}
