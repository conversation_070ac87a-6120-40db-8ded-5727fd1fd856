/**
 * Utility function to get localized chatflow names based on ID
 * Maps specific chatflow IDs to their localized names using i18n
 */

/**
 * Get the localized name for a chatflow based on its ID
 * @param {string} chatflowId - The chatflow ID
 * @param {function} t - The i18n translation function
 * @param {string} fallbackName - Fallback name if no mapping exists
 * @returns {string} The localized chatflow name
 */
export const getChatflowDisplayName = (chatflowId, t, fallbackName = '') => {
  // Check if we have a translation for this specific chatflow ID
  const translationKey = `chatflow.${chatflowId}`
  const translatedName = t(translationKey)

  // If translation exists and is different from the key, use it
  if (translatedName && translatedName !== translationKey) {
    return translatedName
  }

  // Otherwise, return the fallback name
  return fallbackName
}

/**
 * Get all available chatflow mappings for the current language
 * @param {function} t - The i18n translation function
 * @returns {object} Object with chatflow IDs as keys and localized names as values
 */
export const getAllChatflowMappings = (t) => {
  return {
    '57e4146c-dd6b-4eed-ae49-40223b00af25': t('chatflow.57e4146c-dd6b-4eed-ae49-40223b00af25'),
    '248e6488-33f3-4b91-a75f-a0e1f76f286c': t('chatflow.248e6488-33f3-4b91-a75f-a0e1f76f286c')
  }
}

/**
 * Check if a chatflow ID has a custom mapping
 * @param {string} chatflowId - The chatflow ID
 * @returns {boolean} True if the chatflow has a custom mapping
 */
export const hasChatflowMapping = (chatflowId) => {
  const mappedIds = ['57e4146c-dd6b-4eed-ae49-40223b00af25', '248e6488-33f3-4b91-a75f-a0e1f76f286c']
  return mappedIds.includes(chatflowId)
}
