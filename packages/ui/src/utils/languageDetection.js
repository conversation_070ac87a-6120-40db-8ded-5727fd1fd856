/**
 * Language mapping for API response parsing
 * Maps API language names to our supported languages
 */
const LANGUAGE_MAPPING = {
  Vietnamese: 'Vietnamese',
  English: 'English',
  Japanese: 'Japanese',
  Chinese: 'Chinese'
}

/**
 * Parse API response that contains language detection and translation
 * Format: "DetectedLanguage __SEPERATE__ TranslatedText"
 * @param {string} apiResponse - The API response text
 * @returns {Object} - Object containing detected language and translated text
 */
export const parseApiResponse = (apiResponse) => {
  try {
    if (!apiResponse || typeof apiResponse !== 'string') {
      return {
        detectedLanguage: null,
        translatedText: apiResponse || '',
        success: false
      }
    }

    // Check if response contains the separator
    if (apiResponse.includes('__SEPERATE__')) {
      const parts = apiResponse.split('__SEPERATE__')

      if (parts.length >= 2) {
        const detectedLanguage = parts[0].trim()
        const translatedText = parts.slice(1).join('__SEPERATE__').trim() // Handle multiple separators

        // Validate detected language
        const mappedLanguage = LANGUAGE_MAPPING[detectedLanguage]

        return {
          detectedLanguage: mappedLanguage || detectedLanguage,
          translatedText: translatedText,
          success: true,
          originalDetection: detectedLanguage
        }
      }
    }

    // If no separator found, treat entire response as translated text
    return {
      detectedLanguage: null,
      translatedText: apiResponse,
      success: false
    }
  } catch (error) {
    console.error('Error parsing API response:', error)
    return {
      detectedLanguage: null,
      translatedText: apiResponse || '',
      success: false,
      error: error.message
    }
  }
}

/**
 * Legacy detectLanguage function for backward compatibility
 * Now returns a placeholder since API handles detection
 * @param {string} text - Text to detect language for
 * @returns {Promise<{language: string, confidence: number, detected: boolean}>}
 */
export const detectLanguage = async (text) => {
  try {
    // Return early if text is empty or too short
    if (!text || text.trim().length < 3) {
      return {
        language: null,
        confidence: 0,
        detected: false
      }
    }

    // Since API now handles detection, return a placeholder
    // This function is kept for backward compatibility
    return {
      language: null,
      confidence: 0,
      detected: false,
      method: 'api-based'
    }
  } catch (error) {
    console.error('Language detection error:', error)
    return {
      language: null,
      confidence: 0,
      detected: false,
      error: error.message
    }
  }
}

/**
 * Get display name for detected language in Google Translate style
 * @param {string} detectedLanguage - The detected language name
 * @param {function} t - Translation function from i18n
 * @returns {string} - Formatted display name like "Vietnamese - Detected"
 */
export const getDetectedLanguageDisplay = (detectedLanguage, t) => {
  if (!detectedLanguage) {
    return t('languages.detectLanguage')
  }

  // Map English language names to localized names
  const languageDisplayMap = {
    Vietnamese: t('languages.vietnamese'),
    English: t('languages.english'),
    Japanese: t('languages.japanese'),
    Chinese: t('languages.chinese')
  }

  const localizedName = languageDisplayMap[detectedLanguage] || detectedLanguage

  // Return in Google Translate style: "Vietnamese - Detected"
  return `${localizedName} - ${t('languages.detected')}`
}

/**
 * Check if a language is supported for detection
 * @param {string} language - Language name to check
 * @returns {boolean}
 */
export const isSupportedLanguage = (language) => {
  return Object.keys(LANGUAGE_MAPPING).includes(language)
}

/**
 * Normalize language name for consistent processing
 * @param {string} language - Language name to normalize
 * @returns {string} - Normalized language name
 */
export const normalizeLanguageName = (language) => {
  if (!language) return null

  // Handle common variations
  const normalizations = {
    chinese: 'Chinese',
    mandarin: 'Chinese',
    japanese: 'Japanese',
    english: 'English',
    vietnamese: 'Vietnamese'
  }

  return normalizations[language.toLowerCase()] || language
}
