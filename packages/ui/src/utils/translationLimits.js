/**
 * Translation limits and validation utilities
 */

// Translation limits
export const TRANSLATION_LIMITS = {
  TEXT_MAX_CHARACTERS: 10000,
  DOCUMENT_MAX_PAGES: 20,
  WARNING_THRESHOLD: 0.9 // 90% of limit
}

/**
 * Check if text exceeds character limit
 * @param {string} text - Text to check
 * @returns {boolean} - True if exceeds limit
 */
export const isTextLimitExceeded = (text) => {
  if (!text) return false
  return text.length > TRANSLATION_LIMITS.TEXT_MAX_CHARACTERS
}

/**
 * Check if text is approaching character limit (90% threshold)
 * @param {string} text - Text to check
 * @returns {boolean} - True if approaching limit
 */
export const isTextApproachingLimit = (text) => {
  if (!text) return false
  const threshold = TRANSLATION_LIMITS.TEXT_MAX_CHARACTERS * TRANSLATION_LIMITS.WARNING_THRESHOLD
  return text.length >= threshold && text.length <= TRANSLATION_LIMITS.TEXT_MAX_CHARACTERS
}

/**
 * Get character count status
 * @param {string} text - Text to check
 * @returns {Object} - Status object with count, max, isApproaching, isExceeded
 */
export const getCharacterCountStatus = (text) => {
  const count = text ? text.length : 0
  const max = TRANSLATION_LIMITS.TEXT_MAX_CHARACTERS

  return {
    count,
    max,
    isApproaching: isTextApproachingLimit(text),
    isExceeded: isTextLimitExceeded(text),
    percentage: (count / max) * 100
  }
}

/**
 * Check if document exceeds page limit
 * @param {number} pageCount - Number of pages
 * @returns {boolean} - True if exceeds limit
 */
export const isDocumentLimitExceeded = (pageCount) => {
  if (!pageCount || pageCount < 0) return false
  return pageCount > TRANSLATION_LIMITS.DOCUMENT_MAX_PAGES
}

/**
 * Estimate page count from text content (rough estimation)
 * @param {string} text - Text content
 * @returns {number} - Estimated page count
 */
export const estimatePageCount = (text) => {
  if (!text) return 0

  // Rough estimation: ~500 words per page, ~5 characters per word
  const charactersPerPage = 2500
  return Math.ceil(text.length / charactersPerPage)
}

/**
 * Get color for character counter based on status
 * @param {Object} status - Status from getCharacterCountStatus
 * @returns {string} - Color value
 */
export const getCounterColor = (status) => {
  if (status.isExceeded) {
    return '#d93025' // Red for exceeded
  } else if (status.isApproaching) {
    return '#f9ab00' // Orange/yellow for approaching
  } else {
    return '#5f6368' // Gray for normal
  }
}

/**
 * Validate translation request
 * @param {string} text - Text to translate
 * @param {File} file - File to translate (optional)
 * @returns {Object} - Validation result with isValid, errors array
 */
export const validateTranslationRequest = (text, file = null) => {
  const errors = []

  // Check text limits
  if (text && isTextLimitExceeded(text)) {
    errors.push({
      type: 'TEXT_LIMIT_EXCEEDED',
      message: `Văn bản vượt quá giới hạn ${TRANSLATION_LIMITS.TEXT_MAX_CHARACTERS} ký tự`,
      limit: TRANSLATION_LIMITS.TEXT_MAX_CHARACTERS,
      current: text.length
    })
  }

  // Check document limits (if file is provided)
  if (file) {
    // For now, we'll estimate based on file size
    // In a real implementation, you might want to parse the document first
    const estimatedPages = Math.ceil(file.size / 50000) // Rough estimation

    if (isDocumentLimitExceeded(estimatedPages)) {
      errors.push({
        type: 'DOCUMENT_LIMIT_EXCEEDED',
        message: `Tài liệu vượt quá giới hạn ${TRANSLATION_LIMITS.DOCUMENT_MAX_PAGES} trang`,
        limit: TRANSLATION_LIMITS.DOCUMENT_MAX_PAGES,
        current: estimatedPages
      })
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}
