/**
 * Utility functions for role-based access control
 */

// Admin roles that can access admin panel
export const ADMIN_ROLES = ['MASTER_ADMIN', 'SITE_ADMIN']

/**
 * Check if user has admin privileges (MASTER_ADMIN or SITE_ADMIN)
 * @param {Object} user - User object from Redux store
 * @returns {boolean} - True if user is admin, false otherwise
 */
export const isAdmin = (user) => {
  if (!user || !user.role) return false
  return ADMIN_ROLES.includes(user.role)
}

/**
 * Check if user is MASTER_ADMIN
 * @param {Object} user - User object from Redux store
 * @returns {boolean} - True if user is MASTER_ADMIN, false otherwise
 */
export const isMasterAdmin = (user) => {
  return user?.role === 'MASTER_ADMIN'
}

/**
 * Check if user is SITE_ADMIN
 * @param {Object} user - User object from Redux store
 * @returns {boolean} - True if user is SITE_ADMIN, false otherwise
 */
export const isSiteAdmin = (user) => {
  return user?.role === 'SITE_ADMIN'
}

/**
 * Check if user is regular USER (non-admin)
 * @param {Object} user - User object from Redux store
 * @returns {boolean} - True if user is regular USER, false otherwise
 */
export const isRegularUser = (user) => {
  return user?.role === 'USER'
}

/**
 * Get user role display name for UI
 * @param {Object} user - User object from Redux store
 * @returns {string} - Role display name
 */
export const getUserRoleDisplayName = (user) => {
  if (!user || !user.role) return 'Unknown'

  switch (user.role) {
    case 'MASTER_ADMIN':
      return 'Master Admin'
    case 'SITE_ADMIN':
      return 'Site Admin'
    case 'ADMIN':
      return 'Admin'
    case 'USER':
      return 'User'
    default:
      return user.role
  }
}
