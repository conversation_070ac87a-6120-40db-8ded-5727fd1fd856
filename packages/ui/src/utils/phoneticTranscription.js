import pinyinLib from 'pinyin'
import * as kuromoji from '@patdx/kuromoji'

// Use the correct pinyin function
const pinyin = pinyinLib.default || pinyinLib

// Katakana to Romaji conversion map (Hepburn system)
const katakanaToRomajiMap = {
  // Basic (Gojūon)
  ア: 'a',
  イ: 'i',
  ウ: 'u',
  エ: 'e',
  オ: 'o',
  カ: 'ka',
  キ: 'ki',
  ク: 'ku',
  ケ: 'ke',
  コ: 'ko',
  サ: 'sa',
  シ: 'shi',
  ス: 'su',
  セ: 'se',
  ソ: 'so',
  タ: 'ta',
  チ: 'chi',
  ツ: 'tsu',
  テ: 'te',
  ト: 'to',
  ナ: 'na',
  ニ: 'ni',
  ヌ: 'nu',
  ネ: 'ne',
  ノ: 'no',
  ハ: 'ha',
  ヒ: 'hi',
  フ: 'fu',
  ヘ: 'he',
  ホ: 'ho',
  マ: 'ma',
  ミ: 'mi',
  ム: 'mu',
  メ: 'me',
  モ: 'mo',
  ヤ: 'ya',
  ユ: 'yu',
  ヨ: 'yo',
  ラ: 'ra',
  リ: 'ri',
  ル: 'ru',
  レ: 're',
  ロ: 'ro',
  ワ: 'wa',
  ヲ: 'o',
  ン: 'n',

  // Dakuten
  ガ: 'ga',
  ギ: 'gi',
  グ: 'gu',
  ゲ: 'ge',
  ゴ: 'go',
  ザ: 'za',
  ジ: 'ji',
  ズ: 'zu',
  ゼ: 'ze',
  ゾ: 'zo',
  ダ: 'da',
  ヂ: 'ji',
  ヅ: 'zu',
  デ: 'de',
  ド: 'do',
  バ: 'ba',
  ビ: 'bi',
  ブ: 'bu',
  ベ: 'be',
  ボ: 'bo',

  // Handakuten
  パ: 'pa',
  ピ: 'pi',
  プ: 'pu',
  ペ: 'pe',
  ポ: 'po',

  // Yōon
  キャ: 'kya',
  キュ: 'kyu',
  キョ: 'kyo',
  シャ: 'sha',
  シュ: 'shu',
  ショ: 'sho',
  チャ: 'cha',
  チュ: 'chu',
  チョ: 'cho',
  ニャ: 'nya',
  ニュ: 'nyu',
  ニョ: 'nyo',
  ヒャ: 'hya',
  ヒュ: 'hyu',
  ヒョ: 'hyo',
  ミャ: 'mya',
  ミュ: 'myu',
  ミョ: 'myo',
  リャ: 'rya',
  リュ: 'ryu',
  リョ: 'ryo',
  ギャ: 'gya',
  ギュ: 'gyu',
  ギョ: 'gyo',
  ジャ: 'ja',
  ジュ: 'ju',
  ジョ: 'jo',
  ビャ: 'bya',
  ビュ: 'byu',
  ビョ: 'byo',
  ピャ: 'pya',
  ピュ: 'pyu',
  ピョ: 'pyo',

  // Small vowels (used in combinations)
  ァ: 'a',
  ィ: 'i',
  ゥ: 'u',
  ェ: 'e',
  ォ: 'o',
  ャ: 'ya',
  ュ: 'yu',
  ョ: 'yo',
  ヮ: 'wa',

  // Extended foreign sounds
  ウァ: 'wa',
  ウィ: 'wi',
  ウェ: 'we',
  ウォ: 'wo',
  ファ: 'fa',
  フィ: 'fi',
  フェ: 'fe',
  フォ: 'fo',
  ティ: 'ti',
  ディ: 'di',
  ツァ: 'tsa',
  ツィ: 'tsi',
  ツェ: 'tse',
  ツォ: 'tso',
  チェ: 'che',
  ジェ: 'je',
  トゥ: 'tu',
  ドゥ: 'du',
  クァ: 'kwa',
  クィ: 'kwi',
  クェ: 'kwe',
  クォ: 'kwo',
  グァ: 'gwa',
  グィ: 'gwi',
  グェ: 'gwe',
  グォ: 'gwo',
  ヴァ: 'va',
  ヴィ: 'vi',
  ヴ: 'vu',
  ヴェ: 've',
  ヴォ: 'vo',
  ヴュ: 'vyu',
  ヴャ: 'vya',
  ヴョ: 'vyo',

  // Additional modern combinations
  イェ: 'ye',
  ウュ: 'wyu',
  テャ: 'tya',
  テュ: 'tyu',
  テョ: 'tyo',
  デャ: 'dya',
  デュ: 'dyu',
  デョ: 'dyo',
  スャ: 'sya',
  スュ: 'syu',
  スョ: 'syo',
  ズャ: 'zya',
  ズュ: 'zyu',
  ズョ: 'zyo',

  // Rare/obsolete combinations for transliteration
  シェ: 'she',
  スィ: 'si',
  ズィ: 'zi',
  ニェ: 'nye',
  ヒェ: 'hye',
  ビェ: 'bye',
  ピェ: 'pye',
  フャ: 'fya',
  フュ: 'fyu',
  フョ: 'fyo',
  ホゥ: 'hu',
  ミェ: 'mye',
  リエ: 'rye',
  ラ゚: 'la',
  リ゚: 'li',
  ル゚: 'lu',
  レ゚: 'le',
  ロ゚: 'lo',

  // Sokuon and chōonpu handled via logic
  ッ: '', // mark to double next consonant
  ー: '' // vowel-length marker (use macron in output)
}
/**
 * Convert katakana text to romaji using Hepburn system
 * @param {string} katakana - Katakana text to convert
 * @returns {string} - Romaji text
 */
const katakanaToRomaji = (katakana) => {
  if (!katakana || typeof katakana !== 'string') {
    return ''
  }

  let result = ''
  let i = 0

  while (i < katakana.length) {
    const currentChar = katakana[i]

    // Handle ッ (sokuon) - doubles the next consonant
    if (currentChar === 'ッ') {
      if (i < katakana.length - 1) {
        const nextChar = katakana[i + 1]
        const nextRomaji = katakanaToRomajiMap[nextChar]
        if (nextRomaji && nextRomaji.length > 0) {
          // Special cases for certain consonants
          if (nextRomaji === 'chi') {
            result += 't' // ッチ becomes tchi
          } else if (nextRomaji === 'tsu') {
            result += 't' // ッツ becomes ttsu
          } else {
            // Double the first consonant of the next character
            const firstConsonant = nextRomaji[0]
            if (
              firstConsonant !== 'a' &&
              firstConsonant !== 'i' &&
              firstConsonant !== 'u' &&
              firstConsonant !== 'e' &&
              firstConsonant !== 'o'
            ) {
              result += firstConsonant
            }
          }
        }
      }
      i++
      continue
    }

    // Handle ー (chōonpu) - extends the previous vowel
    if (currentChar === 'ー') {
      if (result.length > 0) {
        const lastChar = result[result.length - 1]
        // Add macron to extend vowel sound
        const vowelMap = {
          a: 'ā',
          i: 'ī',
          u: 'ū',
          e: 'ē',
          o: 'ō'
        }
        if (vowelMap[lastChar]) {
          result = result.slice(0, -1) + vowelMap[lastChar]
        } else {
          // If last character is not a basic vowel, just extend with the same vowel
          result += lastChar
        }
      }
      i++
      continue
    }

    // Check for 3-character combinations first (if any exist)
    if (i < katakana.length - 2) {
      const threeChar = katakana.substring(i, i + 3)
      if (katakanaToRomajiMap[threeChar]) {
        result += katakanaToRomajiMap[threeChar]
        i += 3
        continue
      }
    }

    // Check for 2-character combinations
    if (i < katakana.length - 1) {
      const twoChar = katakana.substring(i, i + 2)
      if (katakanaToRomajiMap[twoChar]) {
        result += katakanaToRomajiMap[twoChar]
        i += 2
        continue
      }
    }

    // Check for single character
    if (katakanaToRomajiMap[currentChar]) {
      result += katakanaToRomajiMap[currentChar]
    } else {
      // If not found in map, keep the original character
      result += currentChar
    }
    i++
  }

  return result
}

// Initialize Kuromoji tokenizer instance
let tokenizer = null
export const initKuromoji = async () => {
  if (!tokenizer) {
    console.log('Initializing Kuromoji...')

    // Custom loader for browser environment
    const myLoader = {
      async loadArrayBuffer(url) {
        // Strip off .gz extension since we're using uncompressed files
        url = url.replace('.gz', '')
        const res = await fetch('https://cdn.jsdelivr.net/npm/@aiktb/kuromoji@1.0.2/dict/' + url)
        if (!res.ok) {
          throw new Error(`Failed to fetch ${url}, status: ${res.status}`)
        }
        return res.arrayBuffer()
      }
    }

    tokenizer = await new kuromoji.TokenizerBuilder({
      loader: myLoader
    }).build()

    console.log('Kuromoji initialized successfully!')
  }
  return tokenizer
}

/**
 * Convert Chinese text to Pinyin with tone marks
 * @param {string} text - Chinese text to convert
 * @returns {string} - Pinyin with tone marks
 */
export const chineseToPinyin = (text) => {
  try {
    if (!text || typeof text !== 'string') {
      return ''
    }

    // Remove non-Chinese characters for processing but keep them in result
    const chineseRegex = /[\u4e00-\u9fff]/g
    const matches = text.match(chineseRegex)

    if (!matches || matches.length === 0) {
      return ''
    }

    // Convert Chinese characters to Pinyin with tone marks
    const pinyinResult = pinyin(text, {
      style: pinyin.STYLE_TONE, // Use tone marks (ā, á, ǎ, à)
      heteronym: false, // Use most common pronunciation
      segment: true // Enable word segmentation
    })

    // Flatten and join the pinyin result
    return pinyinResult.map((item) => (Array.isArray(item) ? item[0] : item)).join(' ')
  } catch (error) {
    console.error('Error converting Chinese to Pinyin:', error)
    return ''
  }
}

/**
 * Convert Japanese text to Romaji using Kuromoji tokenizer
 * @param {string} text - Japanese text to convert
 * @returns {Promise<string>} - Romaji transcription using Hepburn system
 */
export const japaneseToRomaji = async (text) => {
  try {
    if (!text || typeof text !== 'string') {
      return ''
    }

    // Initialize Kuromoji if not already done
    const kuromojiInstance = await initKuromoji()

    // Tokenize the text
    const tokens = kuromojiInstance.tokenize(text)

    // Extract readings and convert to romaji
    const romajiParts = tokens.map((token) => {
      // Use reading if available, otherwise use surface form
      const reading = token.reading || token.surface_form
      // Convert katakana reading to romaji using simple conversion
      return katakanaToRomaji(reading)
    })

    const romajiResult = romajiParts.join(' ')

    return romajiResult
  } catch (error) {
    console.error('Error converting Japanese to Romaji:', error)
    return ''
  }
}

/**
 * Get phonetic transcription based on language
 * @param {string} text - Text to convert
 * @param {string} language - Target language (localized language name)
 * @param {function} t - Translation function from i18n
 * @returns {Promise<string>} - Phonetic transcription
 */
export const getPhoneticTranscription = async (text, language, t = null) => {
  try {
    if (!text || !language) {
      return ''
    }

    // If translation function is provided, use i18n keys
    if (t) {
      if (language === t('languages.chinese') || language === 'Chinese') {
        return chineseToPinyin(text)
      } else if (language === t('languages.japanese') || language === 'Japanese') {
        return await japaneseToRomaji(text)
      }
    } else {
      // Fallback to hardcoded values for backward compatibility
      switch (language) {
        case 'Trung':
        case 'Chinese':
        case '中文':
        case '中国語':
          return chineseToPinyin(text)
        case 'Nhật':
        case 'Japanese':
        case '日本語':
        case '日语':
          return await japaneseToRomaji(text)
        default:
          return ''
      }
    }

    return ''
  } catch (error) {
    console.error('Error getting phonetic transcription:', error)
    return ''
  }
}

/**
 * Check if a language supports phonetic transcription
 * @param {string} language - Language to check (localized language name)
 * @param {function} t - Translation function from i18n
 * @returns {boolean} - Whether the language supports phonetic transcription
 */
export const supportsPhoneticTranscription = (language, t = null) => {
  // If translation function is provided, use i18n keys
  if (t) {
    // Check both localized names and API detected language names
    return language === t('languages.chinese') || language === t('languages.japanese') || language === 'Chinese' || language === 'Japanese'
  }

  // Fallback to hardcoded values for backward compatibility
  const supportedLanguages = [
    'Trung',
    'Chinese',
    '中文',
    '中国語', // Chinese variants
    'Nhật',
    'Japanese',
    '日本語',
    '日语' // Japanese variants
  ]

  return supportedLanguages.includes(language)
}

/**
 * Format text with phonetic transcription for display
 * @param {string} originalText - Original translated text
 * @param {string} phoneticText - Phonetic transcription
 * @param {string} language - Target language
 * @returns {object} - Formatted display object
 */
export const formatPhoneticDisplay = (originalText, phoneticText, language) => {
  return {
    original: originalText,
    phonetic: phoneticText,
    language: language,
    hasPhonetic: !!phoneticText && phoneticText.trim().length > 0
  }
}
