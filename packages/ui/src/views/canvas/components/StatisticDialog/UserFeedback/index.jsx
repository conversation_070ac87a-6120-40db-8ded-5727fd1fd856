import { getCustomerFeedback } from '@/api/dashboard'
import { Card, CardContent, CircularProgress, Grid, LinearProgress, Paper, Stack, Typography } from '@mui/material'
import PropTypes from 'prop-types'
import { memo, useCallback, useEffect, useState } from 'react'
import TableFeedback from './TableFeedback'

const UserFeedback = ({ flowId }) => {
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState({
    positive: 0,
    negative: 0,
    total: 0
  })

  const handleGetCustomerFeedback = useCallback(async () => {
    if (!flowId) return

    setLoading(true)
    try {
      const res = await getCustomerFeedback({ flowId })
      if (res?.data?.totalFeedback) {
        setData({
          positive: res.data.positive,
          negative: res.data.negative,
          total: res.data.totalFeedback
        })
      }
    } catch (error) {
      console.error(error)
    } finally {
      setLoading(false)
    }
  }, [flowId])

  const ViewLinePositiveNegative = useCallback(
    (type) => {
      return (
        <Stack direction='row' spacing={1} alignItems='center'>
          <Typography variant='h5' color='grey.500'>
            {type === 'positive' ? 'Tích cực' : 'Tiêu cực'}
          </Typography>
          <Paper
            sx={{ color: type === 'positive' ? '#005FAB' : '#F7941E' }}
            style={{ width: `${((type === 'positive' ? data.positive : data.negative) / data.total) * 100}%`, maxWidth: '100%' }}
          >
            <LinearProgress variant='determinate' color='inherit' value={100} className='w-full h-2 rounded' />
          </Paper>
          <Typography variant='h5' color='grey.600'>
            {type === 'positive' ? data.positive : data.negative}
          </Typography>
        </Stack>
      )
    },
    [data]
  )

  useEffect(() => {
    handleGetCustomerFeedback()
  }, [handleGetCustomerFeedback])

  return (
    <Card variant='outlined'>
      <CardContent sx={{ padding: '16px !important' }}>
        <Typography variant='h4' color='#005FAB' className='mb-2'>
          Phản hồi của khách hàng
        </Typography>
        <Paper>
          {loading ? (
            <CircularProgress color='primary' className='mb-4' />
          ) : (
            <Stack spacing={1} className='mb-4'>
              <Typography variant='h3' color='#222222'>
                {data.total}
              </Typography>
              {ViewLinePositiveNegative('positive')}
              {ViewLinePositiveNegative('negative')}
            </Stack>
          )}
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <TableFeedback type='positive' flowId={flowId} />
            </Grid>
            <Grid item xs={12} md={6}>
              <TableFeedback type='negative' flowId={flowId} />
            </Grid>
          </Grid>
        </Paper>
      </CardContent>
    </Card>
  )
}

UserFeedback.propTypes = {
  flowId: PropTypes.string
}

export default memo(UserFeedback)
