import { getCustomerFeedback } from '@/api/dashboard'
import ComponentLoader from '@/ui-component/loading/ComponentLoader'
import {
  Card,
  CardContent,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  Tooltip,
  Typography
} from '@mui/material'
import clsx from 'clsx'
import dayjs from 'dayjs'
import PropTypes from 'prop-types'
import { memo, useCallback, useEffect, useState } from 'react'
import ChatDetail from '../../HistoryDialog/ChatDetail'

const TH = ['STT', 'ID', 'Nội dung phản hồi', 'Thời gian']

const DefaultMessage = [
  {
    id: '3c3fe7d8-bbcd-4ec1-b448-bc3fdb9a018f',
    isFAQ: false,
    name: '',
    attachments: [],
    question: 'chuyển từ LOS sang ACL',
    answer:
      'Tôi không tìm thấy thông tin phù hợp trong kho dữ liệu về quy trình chuyển từ LOS sang ACL. Anh/Chị có thể mô tả rõ hơn về vấn đề cần hỗ trợ không? Hoặc Anh/Chị có thể cho biết thêm chi tiết về nghiệp vụ cụ thể liên quan đến việc chuyển từ LOS sang ACL không?',
    questionTtype: 'Khác',
    chatId: 'ba104f7b-9e37-49bc-95dc-62f6db862234',
    createdDate: '2025-04-11T03:05:20.411Z',
    updatedDate: '10:05:20 11/04/2025',
    beingProcessed: false,
    timeLastCron: '2025-04-11T02:50:14.065Z'
  }
]

const TableFeedback = ({ type, flowId }) => {
  const [page, setPage] = useState(0)
  const [limit, setLimit] = useState(10)
  const [loading, setLoading] = useState(false)
  const [QA, setQA] = useState({
    data: {
      messageId: null,
      messages: DefaultMessage
    },
    open: false
  })
  const [feedbacks, setFeedbacks] = useState({
    data: [],
    total: 0
  })

  const handlePageChange = (event, newPage) => {
    setPage(newPage)
  }

  const handleRowsPerPageChange = (event) => {
    const newLimit = parseInt(event.target.value, 10)
    setLimit(newLimit)
    setPage(0)
  }

  const handleGetTypeOfFeedback = useCallback(async () => {
    if (!flowId || !type) return

    setLoading(true)
    try {
      const res = await getCustomerFeedback({ flowId, type, page: page + 1, limit })
      if (res?.data?.items?.length) {
        setFeedbacks({
          data: res.data.items.map((item) => ({ ...item, date: dayjs(item.date).format('HH:mm:ss DD/MM/YYYY') })),
          total: res.data.total
        })
      }
    } catch (error) {
      console.error(error)
    } finally {
      setLoading(false)
    }
  }, [flowId, type, page, limit])

  const handleShowQA = async (message) => {
    setQA({
      open: true,
      data: {
        messageId: message.messageId,
        messages: [message]
      }
    })
  }

  useEffect(() => {
    handleGetTypeOfFeedback()
  }, [handleGetTypeOfFeedback])

  return (
    <Card variant='outlined'>
      <CardContent sx={{ padding: '16px !important' }}>
        <Stack spacing={1}>
          <Typography variant='h4' color='#005FAB'>
            {type === 'positive' ? 'Tích cực' : 'Tiêu cực'}
          </Typography>
          <Paper className='relative min-h-[200px]'>
            <TableContainer>
              <Table size='small'>
                <TableHead>
                  <TableRow className='bg-slate-100'>
                    {TH.map((th) => (
                      <TableCell key={th} className='whitespace-nowrap'>
                        {th}
                      </TableCell>
                    ))}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {feedbacks.data?.length ? (
                    feedbacks.data.map((feedback, index) => (
                      <TableRow
                        key={feedback.id}
                        onClick={() => handleShowQA(feedback)}
                        className={clsx('cursor-pointer', QA.data?.messageId === feedback.messageId ? 'bg-[#ddeffd]' : 'bg-white')}
                      >
                        <TableCell className='min-w-[50px] max-w-[50px]'>{index + 1 + page * limit}</TableCell>
                        <TableCell className='min-w-[130px] max-w-[130px]'>{feedback.id}</TableCell>
                        <TableCell className="min-w-[350px] max-w-[350px] truncate">
                          <Tooltip title={feedback.content} arrow>
                            <span className="block truncate">
                              {feedback.content.length > 50
                                ? `${feedback.content.slice(0, 50)}...`
                                : feedback.content}
                            </span>
                          </Tooltip>
                        </TableCell>
                        <TableCell className='min-w-[80px] max-w-[80px]'>{feedback.date}</TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={TH.length} className='text-center'>
                        Không có dữ liệu
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
            <TablePagination
              showFirstButton
              showLastButton
              component='div'
              rowsPerPageOptions={[10, 20, 50]}
              count={feedbacks.total}
              rowsPerPage={limit}
              page={page}
              onPageChange={handlePageChange}
              onRowsPerPageChange={handleRowsPerPageChange}
              labelRowsPerPage='Số lượng hiển thị'
              labelDisplayedRows={({ from, to, count }) => `${from}–${to} của ${count}`}
              sx={{
                '& .MuiTablePagination-toolbar': {
                  padding: '8px 0px 0px',
                  minHeight: '32px'
                }
              }}
            />
            <ComponentLoader loading={loading} />
          </Paper>
        </Stack>
      </CardContent>
      <ChatDetail
        data={QA.data}
        open={QA.open}
        onClose={useCallback(
          () =>
            setQA({
              open: false,
              data: {
                messageId: null,
                messages: DefaultMessage
              }
            }),
          []
        )}
      />
    </Card>
  )
}

TableFeedback.propTypes = {
  type: PropTypes.string.isRequired,
  flowId: PropTypes.string
}

export default memo(TableFeedback)
