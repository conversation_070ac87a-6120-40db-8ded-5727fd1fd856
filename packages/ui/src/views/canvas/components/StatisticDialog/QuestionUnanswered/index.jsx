import { getQuestionUnanswered } from '@/api/dashboard'
import ComponentLoader from '@/ui-component/loading/ComponentLoader'
import { PieChartData, PieChartOptions } from '@/views/canvas/components/StatisticDialog/data'
import { Card, CardContent, Paper, Stack, Tooltip, Typography } from '@mui/material'
import { IconEdit } from '@tabler/icons-react'
import { ArcElement, Chart as ChartJS, Tooltip as ChartTooltip, Legend } from 'chart.js'
import ChartDataLabels from 'chartjs-plugin-datalabels'
import PropTypes from 'prop-types'
import { Fragment, memo, useCallback, useEffect, useState } from 'react'
import { Pie } from 'react-chartjs-2'
import ListLabel from './ListLabel'

ChartJS.register(ArcElement, ChartTooltip, Legend, ChartDataLabels)

const DefaultListLabel = {
  open: false,
  refreshChart: false
}

function adjustTo100Percent(values) {
  const total = values.reduce((sum, val) => sum + val, 0)
  let percentages = values.map((val) => +((val / total) * 100).toFixed(2))
  let sum = percentages.reduce((a, b) => a + b, 0)
  let diff = +(100 - sum).toFixed(2)

  const indexToFix = percentages.indexOf(Math.max(...percentages))
  percentages[indexToFix] = +(percentages[indexToFix] + diff).toFixed(2)

  return percentages
}

const QuestionUnanswered = ({ flowId }) => {
  const [data, setData] = useState(PieChartData)
  const [loading, setLoading] = useState(false)
  const [listLabel, setListLabel] = useState(DefaultListLabel)

  const handleListLabel = useCallback((value) => {
    setListLabel((prev) => ({ ...prev, ...value }))
  }, [])

  const handleGetUnansweredQuestion = useCallback(async () => {
    if (!flowId) return

    setLoading(true)
    try {
      const res = await getQuestionUnanswered({ flowId })
      if (res?.data?.data?.length) {
        const labels = res.data.data.map((item) => item.category)
        const data = res.data.data.map((item) => item.total)
        setData({
          labels,
          datasets: [
            {
              ...PieChartData.datasets[0],
              data,
              percents: adjustTo100Percent(data)
            }
          ]
        })
        handleListLabel({ refreshChart: false })
      }
    } catch (error) {
      console.error(error)
    } finally {
      setLoading(false)
    }
  }, [flowId, handleListLabel])

  useEffect(() => {
    if (listLabel.refreshChart) handleGetUnansweredQuestion()
  }, [handleGetUnansweredQuestion, listLabel.refreshChart])

  useEffect(() => {
    handleGetUnansweredQuestion()
  }, [handleGetUnansweredQuestion])

  return (
    <Fragment>
      <Card variant='outlined'>
        <CardContent sx={{ padding: '16px !important' }} className='h-[60vh]'>
          <Stack direction='row' justifyContent='space-between' spacing={2}>
            <Typography variant='h4' color='#005FAB'>
              Số lượng câu hỏi theo lĩnh vực
            </Typography>
            <Tooltip title='Chỉnh sửa'>
              <IconEdit className='cursor-pointer hover:text-[#005FAB]' onClick={() => handleListLabel({ open: true })} />
            </Tooltip>
          </Stack>
          <Paper className='h-[calc(100%-30px)] relative'>
            <Pie
              data={data}
              options={PieChartOptions}
              plugins={[
                {
                  id: 'legendDistance',
                  beforeInit: function (chart) {
                    const fitValue = chart.legend.fit
                    chart.legend.fit = function fit() {
                      fitValue.bind(chart.legend)()
                      return (this.height += 10)
                    }
                  }
                }
              ]}
            />
            <ComponentLoader loading={loading} />
          </Paper>
        </CardContent>
      </Card>
      <ListLabel
        flowId={flowId}
        open={listLabel.open}
        onClose={() => handleListLabel(DefaultListLabel)}
        onRefreshChart={() => handleListLabel({ refreshChart: true })}
      />
    </Fragment>
  )
}

QuestionUnanswered.propTypes = {
  flowId: PropTypes.string
}

export default memo(QuestionUnanswered)
