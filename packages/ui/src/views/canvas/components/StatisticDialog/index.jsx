import { App<PERSON>ar, Dialog, Dialog<PERSON>ontent, Grid, IconButton, Too<PERSON><PERSON>, Typo<PERSON> } from '@mui/material'
import { IconX } from '@tabler/icons-react'
import PropTypes from 'prop-types'
import { memo } from 'react'
import { createPortal } from 'react-dom'
import QuestionOverTime from './QuestionOverTime'
import UserFeedback from './UserFeedback'
import QuestionUnanswered from './QuestionUnanswered'

const StatisticDialog = ({ chatflow, flowId, open, onClose }) => {
  return createPortal(
    <Dialog fullScreen open={open} onClose={onClose}>
      <AppBar sx={{ position: 'relative', background: '#005FAB' }}>
        <Toolbar sx={{ padding: '8px 16px' }}>
          <Typography variant='h3' component='div' color='white' sx={{ flex: 1 }}>
            Thống kê
          </Typography>
          <IconButton edge='end' color='inherit' onClick={onClose}>
            <IconX />
          </IconButton>
        </Toolbar>
      </AppBar>
      <DialogContent className='bg-slate-100' sx={{ padding: '16px' }}>
        <Grid container spacing={2}>
          <Grid item xs={12} md={6} lg={7}>
            <QuestionOverTime flowId={flowId} chatflow={chatflow} />
          </Grid>
          <Grid item xs={12} md={6} lg={5}>
            <QuestionUnanswered flowId={flowId} />
          </Grid>
          <Grid item xs={12}>
            <UserFeedback flowId={flowId} />
          </Grid>
        </Grid>
      </DialogContent>
    </Dialog>,
    document.body
  )
}

StatisticDialog.propTypes = {
  chatflow: PropTypes.any,
  flowId: PropTypes.string,
  open: PropTypes.bool,
  onClose: PropTypes.func.isRequired
}

export default memo(StatisticDialog)
