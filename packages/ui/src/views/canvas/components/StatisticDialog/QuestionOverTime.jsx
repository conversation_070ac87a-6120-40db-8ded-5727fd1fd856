import { getQuestionOverTime } from '@/api/dashboard'
import ComponentLoader from '@/ui-component/loading/ComponentLoader'
import { Card, CardContent, Paper, Stack, TextField, Typography } from '@mui/material'
import { DatePicker, LocalizationProvider } from '@mui/x-date-pickers'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import { BarElement, CategoryScale, Chart as ChartJS, Legend, LinearScale, Tooltip } from 'chart.js'
import ChartDataLabels from 'chartjs-plugin-datalabels'
import ZoomPlugin from 'chartjs-plugin-zoom'
import dayjs from 'dayjs'
import PropTypes from 'prop-types'
import { memo, useCallback, useEffect, useState } from 'react'
import { Bar } from 'react-chartjs-2'
import { BarChartData, BarChartOptions } from './data'

ChartJS.register(CategoryScale, LinearScale, BarElement, Tooltip, Legend, ZoomPlugin, ChartDataLabels)

function createUTCDateOnly(year, month, day) {
  return new Date(Date.UTC(year, month, day))
}

const QuestionOverTime = ({ flowId, chatflow }) => {
  const chatflowCreatedData = new Date(chatflow?.createdDate)
  const minDate = createUTCDateOnly(
    chatflowCreatedData.getUTCFullYear(),
    chatflowCreatedData.getUTCMonth(),
    chatflowCreatedData.getUTCDate()
  )
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState(BarChartData)
  const [rangeDate, setRangeDate] = useState({
    fromDate: dayjs().subtract(6, 'days').startOf('day').isAfter(minDate)
      ? dayjs().subtract(6, 'days').startOf('day')
      : dayjs(minDate).startOf('day'),
    toDate: dayjs().endOf('day')
  })

  const now = new Date()
  const maxDate = createUTCDateOnly(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate())

  const onChangeRangeDate = (key, value) => {
    if (key === 'fromDate' && value > rangeDate.toDate) setRangeDate((prev) => ({ ...prev, [key]: value, toDate: null }))
    else if (key === 'fromDate' && value < minDate)
      setRangeDate((prev) => ({ ...prev, [key]: value, fromDate: dayjs(minDate).startOf('day') }))
    else if (key === 'toDate' && value > maxDate) setRangeDate((prev) => ({ ...prev, [key]: value, toDate: dayjs(maxDate).endOf('day') }))
    else if (key === 'toDate' && rangeDate.fromDate && value < rangeDate.fromDate)
      setRangeDate((prev) => ({ ...prev, [key]: value, toDate: rangeDate.fromDate.add(1, 'day').endOf('day') }))
    else if (!value.isValid()) {
      setRangeDate((prev) => ({ ...prev, [key]: value }))
    } else if (key === 'fromDate') {
      setRangeDate((prev) => ({ ...prev, [key]: dayjs(value).startOf('day') }))
    } else setRangeDate((prev) => ({ ...prev, [key]: dayjs(value).endOf('day') }))
  }

  const handleGetQuestionOverTime = useCallback(async () => {
    if (!flowId || !rangeDate.fromDate || !rangeDate.toDate || !rangeDate.fromDate.isValid() || !rangeDate.toDate.isValid()) return

    setLoading(true)
    try {
      const fromDate = rangeDate.fromDate.format('YYYY-MM-DDTHH:mm:ssZ')
      const toDate = rangeDate.toDate.format('YYYY-MM-DDTHH:mm:ssZ')
      const res = await getQuestionOverTime({ flowId, fromDate, toDate })
      if (Array.isArray(res?.data?.data)) {
        const labels = res.data.data.map((item) => dayjs(item.label).format('DD/MM/YYYY'))
        const data = res.data.data.map((item) => item.value)
        setData({
          labels,
          datasets: [{ ...BarChartData.datasets[0], data }]
        })
      }
    } catch (error) {
      console.error(error)
    } finally {
      setLoading(false)
    }
  }, [rangeDate.fromDate, rangeDate.toDate, flowId])

  useEffect(() => {
    handleGetQuestionOverTime()
  }, [handleGetQuestionOverTime])

  return (
    <Card variant='outlined'>
      <CardContent sx={{ padding: '16px !important' }} className='h-[60vh]'>
        <Stack direction='row' justifyContent='space-between' spacing={2}>
          <Typography variant='h4' color='#005FAB'>
            Số lượng câu hỏi theo thời gian
          </Typography>
          <Stack direction='row' spacing={2}>
            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <DatePicker
                label='Từ ngày'
                inputFormat='DD/MM/YYYY'
                value={rangeDate.fromDate}
                onChange={(value) => onChangeRangeDate('fromDate', value)}
                minDate={minDate}
                maxDate={maxDate}
                renderInput={(params) => <TextField size='small' sx={{ width: '160px' }} {...params} />}
              />
              <DatePicker
                label='Đến ngày'
                inputFormat='DD/MM/YYYY'
                minDate={rangeDate.fromDate}
                maxDate={maxDate}
                value={rangeDate.toDate}
                onChange={(value) => onChangeRangeDate('toDate', value)}
                renderInput={(params) => <TextField size='small' sx={{ width: '160px' }} {...params} />}
              />
            </LocalizationProvider>
          </Stack>
        </Stack>
        <Paper className='h-[calc(100%-50px)] relative'>
          <Bar
            data={data}
            options={BarChartOptions}
            plugins={[
              {
                id: 'legendDistance',
                beforeInit: function (chart) {
                  const fitValue = chart.legend.fit
                  chart.legend.fit = function fit() {
                    fitValue.bind(chart.legend)()
                    return (this.height += 20)
                  }
                }
              }
            ]}
          />
          <ComponentLoader loading={loading} />
        </Paper>
      </CardContent>
    </Card>
  )
}

QuestionOverTime.propTypes = {
  flowId: PropTypes.string,
  chatflow: PropTypes.any
}

export default memo(QuestionOverTime)
