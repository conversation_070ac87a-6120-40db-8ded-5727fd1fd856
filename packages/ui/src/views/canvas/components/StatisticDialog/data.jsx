const ChartColors = [
  '#005FAB',
  '#2F6E94',
  '#5E7D7C',
  '#8D8D65',
  '#BC9C4D',
  '#EBAB36',
  '#F7941E',
  '#3B81F6',
  '#1ABC9C',
  '#9B59B6',
  '#E74C3C',
  '#34495E',
  '#16A085',
  '#27AE60',
  '#8E44AD',
  '#2C3E50',
  '#F1C40F',
  '#E67E22',
  '#D35400',
  '#C0392B',
  '#BDC3C7',
  '#7D3C98',
  '#2980B9',
  '#1E8449',
  '#D98880',
  '#A93226',
  '#884EA0',
  '#17A589',
  '#D4AC0D',
  '#CA6F1E',
  '#BA4A00',
  '#641E16',
  '#512E5F',
  '#0E6655',
  '#196F3D'
]

export const BarChartData = {
  labels: [],
  datasets: [
    {
      label: 'Số câu hỏi',
      data: [],
      backgroundColor: '#F7941E',
      barPercentage: 0.5
    }
  ]
}

export const BarChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  scales: {
    y: {
      beginAtZero: true
    }
  },
  plugins: {
    legend: {
      position: 'top'
    },
    datalabels: {
      display: true,
      color: 'black',
      formatter: Math.round,
      anchor: 'end',
      offset: -20,
      align: 'start'
    },
    zoom: {
      pan: {
        enabled: true,
        mode: 'x'
      },
      zoom: {
        wheel: {
          enabled: true
        },
        pinch: {
          enabled: true
        },
        mode: 'x'
      }
    }
  }
}
export const PieChartData = {
  labels: [],
  datasets: [
    {
      label: 'Số câu hỏi',
      data: [],
      backgroundColor: ChartColors
    }
  ]
}

export const PieChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'right'
    },
    datalabels: {
      display: true,
      formatter: (value, ctx) => {
        const percent = ctx.dataset.percents?.[ctx.dataIndex] || 0
        if (percent === 0) return null
        const percentageString = percent % 1 === 0 ? percent : percent.toFixed(2)
        return percentageString + '%'
      },
      anchor: 'center',
      color: '#ffffff',
      backgroundColor: '#00000033',
      borderRadius: 10
    }
  }
}
