import { Dialog, DialogContent, DialogTitle, Typography } from '@mui/material'
import clsx from 'clsx'
import PropTypes from 'prop-types'
import { Fragment, memo } from 'react'
import Markdown from 'react-markdown'

const ChatDetail = ({ data, open, onClose }) => {
  return (
    <Dialog
      fullWidth
      maxWidth='xs'
      scroll='paper'
      open={open}
      onClose={onClose}
      sx={{
        '& .MuiDialog-container': {
          alignItems: 'flex-start'
        }
      }}
    >
      <DialogTitle className='bg-slate-100 flex flex-col gap-1'>
        <Typography className='text-base'>Lịch sử trò chuyện {data?.sessionId ? 'phiên' : ''} </Typography>
      </DialogTitle>
      <DialogContent className='bg-[#FFFFFF]' sx={{ padding: '16px !important' }}>
        {data?.messages?.length > 0 &&
          data?.messages.map((message) => (
            <Fragment key={message.id}>
              {message.question && (
                <Markdown className={clsx('p-2 bg-[#005FAB] text-white mb-3 max-w-[90%] ml-auto rounded w-fit break-words')}>
                  {message.question}
                </Markdown>
              )}
              {message.answer && (
                <Markdown className={clsx('p-2 bg-[#F7F8FF] mb-3 max-w-[90%] mr-auto rounded w-fit break-words')}>
                  {message.answer}
                </Markdown>
              )}
            </Fragment>
          ))}
      </DialogContent>
    </Dialog>
  )
}

ChatDetail.propTypes = {
  data: PropTypes.shape({
    sessionId: PropTypes.any,
    chatId: PropTypes.any,
    messages: PropTypes.arrayOf(PropTypes.object)
  }),
  open: PropTypes.bool,
  onClose: PropTypes.func.isRequired
}

export default memo(ChatDetail)
