import { App<PERSON>ar, Dialog, DialogContent, IconButton, Too<PERSON><PERSON>, Typography } from '@mui/material'
import { IconX } from '@tabler/icons-react'
import PropTypes from 'prop-types'
import { memo } from 'react'
import { createPortal } from 'react-dom'
import ChatHistories from './ChatHistories'

const HistoryDialog = ({ flowId = null, open, onClose }) => {
  return createPortal(
    <Dialog fullScreen open={open} onClose={onClose}>
      <AppBar sx={{ position: 'relative', background: '#005FAB' }}>
        <Toolbar sx={{ padding: '8px 16px' }}>
          <Typography variant='h3' component='div' color='white' sx={{ flex: 1 }}>
            Lịch sử trò chuyện
          </Typography>
          <IconButton edge='end' color='inherit' onClick={onClose}>
            <IconX />
          </IconButton>
        </Toolbar>
      </AppBar>
      <DialogContent className='bg-slate-100' sx={{ padding: '16px' }}>
        {flowId && <ChatHistories isFaq flowId={flowId} />}
      </DialogContent>
    </Dialog>,
    document.body,
    'HistoryDialog'
  )
}

HistoryDialog.propTypes = {
  flowId: PropTypes.string,
  open: PropTypes.bool,
  onClose: PropTypes.func.isRequired,
  isFaq: PropTypes.bool
}

export default memo(HistoryDialog)
