import { getChatSessionsHistory } from '@/api/dashboard'
import { BackdropLoader } from '@/ui-component/loading/BackdropLoader'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore'
import { Accordion, AccordionDetails, AccordionSummary, TablePagination, Typography } from '@mui/material'
import clsx from 'clsx'
import dayjs from 'dayjs'
import PropTypes from 'prop-types'
import { memo, useCallback, useEffect, useState } from 'react'
import ChatDetail from './ChatDetail'
import ChatSession from './ChatSession'

const ChatHistories = ({ flowId }) => {
  const [page, setPage] = useState(0)
  const [limit, setLimit] = useState(10)
  const [loading, setLoading] = useState(false)
  const [chatSession, setChatSession] = useState({ open: false, data: null })
  const [chatSessions, setChatSessions] = useState({
    data: [],
    total: null
  })

  const clickStopPropagation = (e, callback) => {
    e.stopPropagation()
    if (callback) callback()
  }

  const handleChangePage = (event, newPage) => {
    setPage(newPage)
  }

  const handleChangeRowsPerPage = (event) => {
    const newLimit = parseInt(event.target.value, 10)
    setLimit(newLimit)
    setPage(0)
  }

  const handleViewChatSession = (chatSession) => {
    setChatSession({ open: true, data: { ...chatSession, sessionId: chatSession.id, messages: chatSession.message } })
  }

  const handleGetChatSessionsHistory = useCallback(async () => {
    if (!flowId) return

    setLoading(true)
    try {
      const res = await getChatSessionsHistory({ flowId, page: page + 1, limit })
      if (res?.data?.data?.length) setChatSessions(res.data)
    } catch (error) {
      console.error(error)
    } finally {
      setLoading(false)
    }
  }, [flowId, page, limit])

  useEffect(() => {
    handleGetChatSessionsHistory()
  }, [handleGetChatSessionsHistory])

  return (
    <>
      {chatSessions.data.map((session) => (
        <Accordion defaultExpanded key={session.id} className={clsx(chatSession.data?.sessionId === session.id && 'bg-[#dbefff]')}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography className='font-bold' onClick={(e) => clickStopPropagation(e, handleViewChatSession(session))}>
              {dayjs(session.message[0].updatedDate).format('YYYY/MM/DD HH:mm:ss') + ` ${session.message[0].userName || ''}`}
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <ChatSession data={session.message} sessionId={session.id} chatSession={chatSession} setChatSession={setChatSession} />
          </AccordionDetails>
        </Accordion>
      ))}
      {chatSessions.total && (
        <TablePagination
          showFirstButton
          showLastButton
          component='div'
          rowsPerPageOptions={[10, 20, 50]}
          count={chatSessions.total}
          rowsPerPage={limit}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          labelRowsPerPage='Số lượng hiển thị'
          labelDisplayedRows={({ from, to, count }) => `${from}–${to} của ${count}`}
        />
      )}
      <ChatDetail
        data={chatSession.data}
        open={chatSession.open}
        onClose={useCallback(() => setChatSession({ open: false, data: null }), [])}
      />
      <BackdropLoader open={loading} />
    </>
  )
}

ChatHistories.propTypes = {
  flowId: PropTypes.string
}

export default memo(ChatHistories)
