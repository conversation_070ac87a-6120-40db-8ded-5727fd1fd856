import { useEffect, useState } from 'react'
import { Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle, Button, TextField, Grid } from '@mui/material'
import PropTypes from 'prop-types'
import { createTheme, ThemeProvider } from '@mui/material/styles'
import userApi from '@/api/user'
import { useDispatch } from 'react-redux'
import { enqueueSnackbar as enqueueSnackbarAction, closeSnackbar as closeSnackbarAction } from '@/store/actions'
import { IconX } from '@tabler/icons-react'
import AddDocumentDialog from './AddDocumentDialog'

const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2'
    },
    secondary: {
      main: '#dc004e'
    }
  },
  typography: {
    fontFamily: 'Roboto, Arial, sans-serif',
    h6: {
      fontWeight: 600
    },
    body1: {
      fontSize: '1rem'
    }
  },
  spacing: 8
})

const PopupEditGroup = ({ open, onClose, setUserGroups, setSelectedGroup, groupToEdit, selectedGroup }) => {
  const dispatch = useDispatch()
  const enqueueSnackbar = (...args) => dispatch(enqueueSnackbarAction(...args))
  const closeSnackbar = (...args) => dispatch(closeSnackbarAction(...args))

  const [editGroup, setEditGroup] = useState({ groupname: groupToEdit?.groupname })
  // const [displayPrefixes, setDisplayPrefixes] = useState(groupToEdit?.displayPrefixes || '[]')
  // const [openDialogS3, setOpenDialogS3] = useState(false)

  const handleChange = (e) => {
    const { name, value } = e.target
    setEditGroup((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async () => {
    try {
      const resRegisterUser = await userApi.updateGroupUser({ ...editGroup, id: groupToEdit.id })
      const resData = resRegisterUser?.data
      if (resData) {
        setUserGroups((prev) => prev.map((item) => (item.groupname === groupToEdit.groupname ? { ...item, ...resData } : item)))
        setSelectedGroup((prev) => (prev.groupname === groupToEdit.groupname ? { ...prev, ...resData } : prev))
        enqueueSnackbar({
          message: 'Nhóm đã được cập nhật thành công.',
          options: {
            variant: 'success',
            action: (key) => (
              <Button style={{ color: 'white' }} onClick={() => closeSnackbar(key)}>
                <IconX />
              </Button>
            )
          }
        })
      }
      return onClose()
    } catch (error) {
      const msg = error?.response?.data?.message ? error.response.data.message : 'Cập nhật nhóm thất bại.'
      return enqueueSnackbar({
        message: msg,
        options: {
          variant: 'error',
          action: (key) => (
            <Button style={{ color: 'white' }} onClick={() => closeSnackbar(key)}>
              <IconX />
            </Button>
          )
        }
      })
    }
  }

  useEffect(() => {
    if (groupToEdit) {
      setEditGroup({ groupname: groupToEdit.groupname })
    }
  }, [groupToEdit])

  return (
    <ThemeProvider theme={theme}>
      <Dialog
        open={open}
        onClose={() => {
          onClose()
          setEditGroup({ groupname: '' })
        }}
        fullWidth
        maxWidth='sm'
      >
        {/* <AddDocumentDialog
          open={openDialogS3}
          onClose={() => setOpenDialogS3(false)}
          displayPrefixes={displayPrefixes}
          setDisplayPrefixes={setDisplayPrefixes}
          selectedGroup={selectedGroup}
        /> */}
        <DialogTitle>Chỉnh sửa nhóm</DialogTitle>
        <DialogContent>
          <DialogContentText>Vui lòng nhập thông tin chi tiết của nhóm.</DialogContentText>
          <Grid container spacing={2}>
            {editGroup && (
              <Grid item xs={12}>
                <TextField
                  margin='dense'
                  name='groupname'
                  label='Tên nhóm'
                  fullWidth
                  variant='standard'
                  value={editGroup.groupname || ''}
                  onChange={handleChange}
                  disabled
                />
              </Grid>
            )}
            {/* <Grid item xs={12}>
              <TextField
                margin='dense'
                name='displayPrefixes'
                label='Hiển thị tài liệu được truy cập'
                fullWidth
                variant='standard'
                value={JSON.stringify(displayPrefixes)}
                onClick={() => setOpenDialogS3(true)}
              />
            </Grid> */}
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => {
              onClose()
              setEditGroup({ groupname: '' })
            }}
            color='secondary'
          >
            Hủy
          </Button>
          <Button onClick={handleSubmit} color='primary'>
            Đồng ý
          </Button>
        </DialogActions>
      </Dialog>
    </ThemeProvider>
  )
}

PopupEditGroup.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  setUserGroups: PropTypes.func.isRequired,
  setSelectedGroup: PropTypes.func.isRequired,
  groupToEdit: PropTypes.object,
  selectedGroup: PropTypes.object
}

export default PopupEditGroup
