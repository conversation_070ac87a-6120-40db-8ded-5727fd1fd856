import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useDispatch } from 'react-redux'
import { Box, Typography, CircularProgress, Alert } from '@mui/material'
import { useTranslation } from 'react-i18next'
import useMSAL from '../../hooks/useMSAL'
import { loginAction } from '../../store/actions'
import userApi from '../../api/user'

// Utility for debugging
const debugLog = (message, data = null) => {
  if (process.env.NODE_ENV === 'development') {
    console.log(`🔍 [MicrosoftRedirect]: ${message}`, data || '')
  }
}

const MicrosoftRedirect = () => {
  const { t } = useTranslation()
  const [status, setStatus] = useState('processing')
  const [message, setMessage] = useState('')
  const [isProcessing, setIsProcessing] = useState(false)

  const navigate = useNavigate()
  const dispatch = useDispatch()
  const { handleRedirectPromise, isInitialized, msalInstance, clearCache } = useMSAL()

  const login = (...args) => dispatch(loginAction(...args))

  // Initialize message with translation
  useEffect(() => {
    setMessage(t('microsoft.processingLogin'))
  }, [t])

  // Clear Microsoft cache on specific errors
  const clearMicrosoftCache = () => {
    try {
      clearCache()
      localStorage.removeItem('microsoftOAuthInProgress')
      sessionStorage.clear()
    } catch (error) {
      debugLog('Error clearing cache', error)
    }
  }

  const processRedirect = async () => {
    if (isProcessing) return

    try {
      setIsProcessing(true)
      setMessage(t('microsoft.processingAuth'))
      debugLog('Starting redirect processing')

      // Try to get token from MSAL
      const result = await handleRedirectPromise()

      // If no result, try authorization code fallback
      if (!result) {
        debugLog('No MSAL result, checking for authorization code')

        const urlParams = new URLSearchParams(window.location.hash.substring(1))
        const authCode = urlParams.get('code')

        if (authCode) {
          setMessage(t('microsoft.processingCode'))

          // Exchange code for token
          const exchangeResponse = await userApi.exchangeMicrosoftCode({
            code: authCode,
            redirectUri: window.location.origin + '/redirect'
          })
          console.log('🚀 ~ index.jsx:64 ~ processRedirect ~ exchangeResponse:', exchangeResponse)

          if (exchangeResponse.data?.accessToken) {
            setMessage(t('microsoft.completingLogin'))

            // Login with token
            const loginResponse = await userApi.loginWithMicrosoft({
              accessToken: exchangeResponse.data.accessToken
            })

            if (loginResponse.data?.user) {
              handleSuccessfulLogin(loginResponse.data)
              return
            } else {
              throw new Error(loginResponse.data?.message || t('microsoft.loginFailed'))
            }
          } else {
            throw new Error(exchangeResponse.data?.message || t('microsoft.cannotAuthenticate'))
          }
        }

        // No result and no auth code
        setStatus('error')
        setMessage(t('microsoft.cannotLogin'))
        redirectToLogin(3000)
        return
      }

      // Clear URL hash
      if (window.location.hash) {
        window.history.replaceState(null, '', window.location.pathname)
      }

      // Get access token from result
      const accessToken = result.accessToken
      if (!accessToken) {
        setStatus('error')
        setMessage(t('microsoft.noAccessToken'))
        redirectToLogin(3000)
        return
      }

      setMessage(t('microsoft.completingLogin'))

      // Login with token
      const response = await userApi.loginWithMicrosoft({ accessToken })

      if (response.data?.user) {
        handleSuccessfulLogin(response.data)
      } else {
        throw new Error(response.data?.message || t('microsoft.loginFailed'))
      }
    } catch (error) {
      debugLog('Processing failed', error)

      // Clear cache for specific errors
      if (
        error.message &&
        (error.message.includes('code_verifier') || error.message.includes('PKCE') || error.message.includes('invalid_grant'))
      ) {
        clearMicrosoftCache()
      }

      setStatus('error')
      setMessage(t('microsoft.loginFailed'))
      redirectToLogin(3000)
    } finally {
      setIsProcessing(false)
    }
  }

  // Handle successful login
  const handleSuccessfulLogin = (data) => {
    const loginData = {
      user: data.user,
      accessToken: data.accessToken,
      refreshToken: data.refreshToken
    }

    localStorage.setItem('dataLogin', JSON.stringify(loginData))
    window.dispatchEvent(new Event('dataLoginUpdated'))
    login(data.user)
    localStorage.removeItem('microsoftOAuthInProgress')

    setStatus('success')
    setMessage(t('microsoft.loginSuccess'))
    // Clear specified localStorage items after successful login
    localStorage.removeItem('chatInputHistory')
    localStorage.removeItem('57e4146c-dd6b-4eed-ae49-40223b00af25_EXTERNAL')
    localStorage.removeItem('248e6488-33f3-4b91-a75f-a0e1f76f286c_EXTERNAL')
    setTimeout(() => {
      window.location.href = '/'
    }, 500)
  }

  // Redirect to login page
  const redirectToLogin = (delay = 0) => {
    setTimeout(() => {
      // navigate('/login')
      localStorage.removeItem('microsoftOAuthInProgress')
    }, delay)
  }

  // Process redirect when component mounts
  useEffect(() => {
    if (isInitialized && !isProcessing) {
      processRedirect()
    }
  }, [isInitialized])

  // Add timeout for MSAL initialization
  useEffect(() => {
    const timeout = setTimeout(() => {
      if (!isInitialized && !isProcessing) {
        debugLog('MSAL initialization timeout')
        localStorage.removeItem('microsoftOAuthInProgress')
        setStatus('error')
        setMessage(t('microsoft.initializationTimeout'))
        redirectToLogin(3000)
      }
    }, 30000)

    return () => clearTimeout(timeout)
  }, [isInitialized, isProcessing])

  // Clear flag on unmount if not successful
  useEffect(() => {
    return () => {
      if (status !== 'success') {
        localStorage.removeItem('microsoftOAuthInProgress')
      }
    }
  }, [status])

  // Render component based on status
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh',
        padding: 3
      }}
    >
      {status === 'processing' && (
        <>
          <CircularProgress size={60} sx={{ mb: 3 }} />
          <Typography variant='h5' gutterBottom>
            {message}
          </Typography>
          <Typography variant='body2' color='textSecondary'>
            {t('microsoft.doNotClose')}
          </Typography>
        </>
      )}

      {status === 'success' && (
        <Alert severity='success' sx={{ mb: 3, width: '100%', maxWidth: 500 }}>
          {message}
        </Alert>
      )}

      {status === 'error' && (
        <Alert severity='error' sx={{ mb: 3, width: '100%', maxWidth: 500 }}>
          {message}
        </Alert>
      )}
    </Box>
  )
}

export default MicrosoftRedirect
