import { FullPageChat } from 'cmcts-c-agent-embedding-react'
import { useEffect, useState } from 'react'
import { createTheme, Skeleton, ThemeProvider } from '@mui/material'
import { styled } from '@mui/system'
import { useTranslation } from 'react-i18next'
import chatflowsApi from '@/api/chatflows'
import useApi from '@/hooks/useApi'
import { baseURL } from '@/store/constant'
import PropTypes from 'prop-types'

window.VITE_DOCUMENT_STORE_BASE_URL = '/s3-explorer'

// Custom theme
const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2'
    },
    secondary: {
      main: '#dc004e'
    }
  },
  typography: {
    fontFamily: 'Roboto, sans-serif'
  }
})

// Custom styles
const Root = styled('div')(({ theme }) => ({
  width: '100%',
  height: '100%'
}))

const ErrorMessage = styled('p')(({ theme }) => ({
  color: theme.palette.error.main,
  fontSize: '1.2rem',
  fontWeight: 'bold',
  animation: 'fadeIn 0.5s ease-in-out',
  textAlign: 'center',
  padding: '2rem',
  '@keyframes fadeIn': {
    '0%': {
      opacity: 0
    },
    '100%': {
      opacity: 1
    }
  }
}))

const ChatbotEmbed = ({ chatflowId }) => {
  const { t } = useTranslation()
  const [chatflow, setChatflow] = useState(null)
  const [chatbotTheme, setChatbotTheme] = useState({})
  const [chatbotOverrideConfig, setChatbotOverrideConfig] = useState({})

  const getSpecificChatflowFromPublicApi = useApi(chatflowsApi.getSpecificChatflowFromPublicEndpoint)

  const error = getSpecificChatflowFromPublicApi?.error

  useEffect(() => {
    if (chatflowId) {
      getSpecificChatflowFromPublicApi.request(chatflowId)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [chatflowId])

  useEffect(() => {
    if (getSpecificChatflowFromPublicApi?.data) {
      const chatflowData = getSpecificChatflowFromPublicApi.data
      setChatflow(chatflowData)

      const chatflowType = chatflowData.type
      if (chatflowData.chatbotConfig) {
        let parsedConfig = {}
        if (chatflowType === 'MULTIAGENT') {
          parsedConfig.showAgentMessages = true
        }

        try {
          parsedConfig = { ...parsedConfig, ...JSON.parse(chatflowData.chatbotConfig) }
          setChatbotTheme(parsedConfig)
          if (parsedConfig.overrideConfig) {
            if (parsedConfig.overrideConfig.generateNewSession) {
              parsedConfig.overrideConfig.sessionId = Date.now().toString()
            }
            setChatbotOverrideConfig(parsedConfig.overrideConfig)
          }
        } catch (e) {
          console.error(e)
          setChatbotTheme(parsedConfig)
          setChatbotOverrideConfig({})
        }
      } else if (chatflowType === 'MULTIAGENT') {
        setChatbotTheme({ showAgentMessages: true })
      }
    }
  }, [getSpecificChatflowFromPublicApi.data])

  useEffect(() => {
    if (chatflow?.id) {
      const isUseFAQ = Boolean(chatflow?.flowData.includes('"label":"FAQs","name":"faqs","version":1,"type":"FAQs"'))
      const existingChatflowData = JSON.parse(localStorage?.getItem('isUseFAQs')) || {}
      const updatedChatflowData = { ...existingChatflowData, [chatflow.id]: isUseFAQ }
      localStorage.setItem('isUseFAQs', JSON.stringify(updatedChatflowData))
    }
  }, [chatflow?.id])

  if (!chatflowId) {
    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100%',
          color: '#666',
          fontSize: '16px'
        }}
      >
        {t('chatbot.selectAgent')}
      </div>
    )
  }

  return (
    <ThemeProvider theme={theme}>
      <Root>
        {getSpecificChatflowFromPublicApi.loading ? (
          <div className='w-full flex justify-center'>
            <div
              style={{
                width: '100%',
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                gap: '16px',
                padding: '16px'
              }}
            >
              <Skeleton variant='rectangular' width='100%' height='60px' sx={{ borderRadius: '8px' }} />
              <Skeleton variant='rectangular' width='100%' height='calc(100% - 80px)' sx={{ borderRadius: '8px' }} />
            </div>
          </div>
        ) : (
          <>
            {!chatflow?.id && error ? (
              <div className='flex w-full justify-center'>
                <ErrorMessage>{error?.response?.data?.message || t('chatbot.loadError')}</ErrorMessage>
              </div>
            ) : (
              chatflow?.id && (
                <div style={{ width: '100%', height: '100%' }}>
                  <FullPageChat
                    chatflowid={chatflow?.id}
                    apiHost={baseURL}
                    chatflowConfig={chatbotOverrideConfig}
                    theme={{
                      chatWindow: {
                        ...chatbotTheme,
                        textInput: {
                          placeholder: t('chatbot.inputPlaceholder')
                        },
                        footer: {
                          showFooter: false
                        },
                        buttonFeedback: t('chatbot.buttonFeedback'),
                        titleFeedback: t('chatbot.titleFeedback'),
                        placeholderFeedback: t('chatbot.placeholderFeedback')
                      },
                      bubbleBackgroundColor: chatbotTheme.bubbleBackgroundColor,
                      bubbleTextColor: chatbotTheme?.bubbleTextColor
                    }}
                    isUseFAQ={Boolean(chatflow?.isUseFAQ)}
                  />
                </div>
              )
            )}
          </>
        )}
      </Root>
    </ThemeProvider>
  )
}

ChatbotEmbed.propTypes = {
  chatflowId: PropTypes.string
}

export default ChatbotEmbed
