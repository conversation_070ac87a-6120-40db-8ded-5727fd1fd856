import { useSidebar, useFlows } from '@/views/Chat/hooks/ChatHook'
import { Box, Drawer, Stack, useMediaQuery, useTheme, List, ListItemButton, ListItemText } from '@mui/material'
import { IconLanguage, IconWorld, IconFolderFilled } from '@tabler/icons-react'
import { Fragment, memo, useCallback, useState, useEffect } from 'react'
import { useNavigate, useParams, useLocation } from 'react-router'
import { useTranslation } from 'react-i18next'
import ChatSessions from './ChatSessions'
import DialogFindChatFlow from './DialogFindChatFlow'
import { getChatflowDisplayName } from '@/utils/chatflowMapping'

const ChatSidebars = () => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const location = useLocation()

  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('lg'))

  const { chatFlowId, chatSessionId } = useParams()

  const [sidebar, setSidebar] = useSidebar()
  const [chatFlows] = useFlows()

  const [dialogFindInfo, setDialogFindInfo] = useState({
    open: false
  })

  useEffect(() => {
    // Navigate to translation by default if no specific route
    if (location.pathname === '/chat' || location.pathname === '/chat/') {
      navigate('/chat/translation')
    }
  }, [location.pathname, navigate])

  const handleDialogFindInfo = useCallback((value) => setDialogFindInfo((prev) => ({ ...prev, ...value })), [])

  const backToChat = useCallback(() => {
    navigate('/chat')
  }, [navigate])

  const isTranslationSelected = location.pathname === '/chat/translation'

  return (
    <Fragment>
      <Drawer
        open={sidebar.open}
        variant={isMobile ? 'temporary' : 'persistent'}
        anchor='left'
        onClose={() => setSidebar({ open: false })}
        ModalProps={{
          keepMounted: true // Better performance on mobile
        }}
        sx={{
          width: 300,
          flexShrink: 0,
          '& .MuiDrawer-paper': {
            width: 300,
            boxSizing: 'border-box',
            background: '#ebebeb',
            border: 'none'
          }
        }}
      >
        <Stack direction='row' justifyContent='space-between' alignItems='center' gap={2} className='px-3 h-[56px] min-h-[56px]'>
          <Box onClick={backToChat} className='cursor-pointer'>
            <img alt='logo' width={150} height={30} src='https://www.kyocera.co.jp/_assets2/img/logo.svg' />
          </Box>
          {/* <Stack direction='row' gap={1}>
            <Tooltip title={!chatFlowId ? 'Tìm kiếm Agent' : 'Tìm kiếm đoạn chat'}>
              <IconButton color='inherit' onClick={() => handleDialogFindInfo({ open: true, type: !chatFlowId ? 'flow' : 'session' })}>
                <IconSearch color='#5d5d5d' />
              </IconButton>
            </Tooltip>
            <Tooltip title='Đoạn chat mới' className={clsx(!chatFlowId && 'hidden')}>
              <IconButton color='inherit' onClick={() => chatSessionId && navigate(`/chat/${chatFlowId}`)}>
                <IconEdit color='#5d5d5d' />
              </IconButton>
            </Tooltip>
          </Stack> */}
        </Stack>

        {/* Main Content - Single Column Layout */}
        <Box sx={{ flex: 1, overflow: 'auto', display: 'flex', flexDirection: 'column' }}>
          <List sx={{ py: 0, px: 1 }}>
            {/* Translation Service */}
            <ListItemButton
              selected={isTranslationSelected}
              onClick={() => navigate('/chat/translation')}
              sx={{
                px: 2,
                py: 1.5,
                borderRadius: '8px',
                mb: 0.5,
                '&:hover': { backgroundColor: 'rgba(0,0,0,0.04)' },
                '&.Mui-selected': {
                  backgroundColor: 'rgba(0,0,0,0.08)',
                  '&:hover': { backgroundColor: 'rgba(0,0,0,0.08)' }
                }
              }}
            >
              <IconLanguage size={20} color={isTranslationSelected ? '#1976d2' : '#2196f3'} style={{ marginRight: 12 }} />
              <ListItemText
                primary={t('sidebar.translation')}
                sx={{
                  '& .MuiTypography-root': {
                    fontSize: '14px',
                    fontWeight: 500,
                    color: '#333'
                  }
                }}
              />
            </ListItemButton>

            {/* AI Agents - Direct display without collapse */}
            {chatFlows?.map((flow, i) => {
              const isSelected = location.pathname.includes(flow.id)
              // Get display name using mapping or fallback to original name
              const displayName = getChatflowDisplayName(flow.id, t, flow.name)

              return (
                <ListItemButton
                  key={flow.id}
                  selected={isSelected}
                  onClick={() => navigate(`/chat/${flow.id}`)}
                  sx={{
                    px: 2,
                    py: 1.5,
                    borderRadius: '8px',
                    mb: 0.5,
                    '&:hover': { backgroundColor: 'rgba(0,0,0,0.04)' },
                    '&.Mui-selected': {
                      backgroundColor: 'rgba(0,0,0,0.08)',
                      '&:hover': { backgroundColor: 'rgba(0,0,0,0.08)' }
                    }
                  }}
                >
                  {flow?.id === '57e4146c-dd6b-4eed-ae49-40223b00af25' ? (
                    <IconWorld size={20} color={isSelected ? '#4caf50' : '#66bb6a'} style={{ marginRight: 12 }} />
                  ) : (
                    <IconFolderFilled size={20} color={isSelected ? '#ff9800' : '#ffb74d'} style={{ marginRight: 12 }} />
                  )}
                  <ListItemText
                    primary={displayName}
                    sx={{
                      '& .MuiTypography-root': {
                        fontSize: '14px',
                        fontWeight: 500,
                        color: '#333',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap'
                      }
                    }}
                  />
                </ListItemButton>
              )
            })}
          </List>

          {/* Chat Sessions for selected flow */}
          {chatFlowId && (
            <Box sx={{ borderTop: '1px solid #e0e0e0', pt: 1 }}>
              <ChatSessions />
            </Box>
          )}
        </Box>
      </Drawer>
      <DialogFindChatFlow open={dialogFindInfo.open} onClose={() => handleDialogFindInfo({ open: false })} />
    </Fragment>
  )
}

export default memo(ChatSidebars)
