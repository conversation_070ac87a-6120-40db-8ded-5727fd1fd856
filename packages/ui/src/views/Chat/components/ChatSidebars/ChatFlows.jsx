import ComponentLoader from '@/ui-component/loading/ComponentLoader'
import { useFlows } from '@/views/Chat/hooks/ChatHook'
import { Avatar, List, ListItemAvatar, ListItemButton, ListItemText, ListSubheader } from '@mui/material'
import clsx from 'clsx'
import { memo, useEffect, useRef } from 'react'
import { useLocation, useNavigate, useParams } from 'react-router'
import { useTranslation } from 'react-i18next'
import { ListBgColorWithTextWhite } from './data'
import { getChatflowDisplayName } from '../../../../utils/chatflowMapping'

const ChatFlows = () => {
  const selectedItemRef = useRef(null)
  const { t } = useTranslation()

  const location = useLocation()
  const navigate = useNavigate()
  const { chatFlowId, chatSessionId } = useParams()

  const [chatFlows] = useFlows()

  useEffect(() => {
    if (selectedItemRef.current) {
      selectedItemRef.current.scrollIntoView({
        block: 'center'
      })
    }
  }, [chatFlowId, chatSessionId])

  return (
    <List
      sx={{
        py: 0,
        px: 1,
        maxHeight: 'calc(100vh - 400px)',
        overflowY: 'auto',
        overflowX: 'hidden',
        scrollbarColor: '#d0d0d0 #ffffff00',
        '&::-webkit-scrollbar': {
          width: '4px'
        },
        '&::-webkit-scrollbar-track': {
          background: 'transparent'
        },
        '&::-webkit-scrollbar-thumb': {
          background: '#d0d0d0',
          borderRadius: '2px'
        }
      }}
    >
      {chatFlows?.map((flow, i) => {
        const isSelected = location.pathname.includes(flow.id)
        // Get display name using mapping or fallback to original name
        const displayName = getChatflowDisplayName(flow.id, t, flow.name)

        return (
          <ListItemButton
            ref={isSelected ? selectedItemRef : null}
            key={flow.id}
            title={displayName}
            disableRipple={true}
            sx={{
              padding: '8px 12px',
              borderRadius: '8px',
              background: isSelected ? '#ffffff' : 'transparent',
              marginBottom: '2px',
              border: isSelected ? '1px solid #e0e0e0' : '1px solid transparent',
              boxShadow: isSelected ? '0 1px 3px rgba(0,0,0,0.1)' : 'none',
              '&:hover': {
                background: isSelected ? '#ffffff' : 'rgba(255,255,255,0.7)',
                border: '1px solid #e0e0e0'
              }
            }}
            onClick={() => !isSelected && navigate(`/chat/${flow.id}`)}
          >
            <ListItemAvatar sx={{ minWidth: 36, margin: 0 }}>
              <Avatar
                variant='rounded'
                sx={{
                  bgcolor: ListBgColorWithTextWhite[i % ListBgColorWithTextWhite.length],
                  color: 'white',
                  width: '32px',
                  height: '32px',
                  fontSize: '12px',
                  fontWeight: 600,
                  borderRadius: '8px'
                }}
              >
                {displayName
                  .toUpperCase()
                  .replace(/[^A-Z1-9]/g, '')
                  .slice(0, 2)}
              </Avatar>
            </ListItemAvatar>
            <ListItemText
              sx={{
                margin: 0,
                '& .MuiTypography-root': {
                  display: 'block',
                  textOverflow: 'ellipsis',
                  overflow: 'hidden',
                  whiteSpace: 'nowrap',
                  fontSize: '13px',
                  fontWeight: 500,
                  color: '#333'
                }
              }}
            >
              {displayName}
            </ListItemText>
          </ListItemButton>
        )
      })}
      <ComponentLoader transparent={true} loading={!chatFlows?.length} />
    </List>
  )
}

export default memo(ChatFlows)
