import { useSessions } from '@/views/Chat/hooks/ChatHook'
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  List,
  ListItemButton,
  ListItemSecondaryAction,
  ListItemText,
  ListSubheader,
  Menu,
  MenuItem,
  Stack,
  Typography
} from '@mui/material'
import { IconDots, IconPencil, IconTrash } from '@tabler/icons-react'
import clsx from 'clsx'
import { memo, useEffect, useState } from 'react'
import { useLocation, useNavigate, useParams } from 'react-router'
import { useTranslation } from 'react-i18next'

const ChatSessions = () => {
  const { t } = useTranslation()
  const location = useLocation()
  const navigate = useNavigate()

  const { chatFlowId, chatSessionId } = useParams()

  const [chatSessions, setChatSessions, deleteChatSession] = useSessions()

  const [anchorEl, setAnchorEl] = useState(null)
  const [renameSession, setRenameSession] = useState(null)
  const [deleteSession, setDeleteSession] = useState(null)
  const [sessionAction, setSessionAction] = useState({
    open: false,
    session: null
  })

  const handleSessionAction = (e, session) => {
    e.stopPropagation()
    setAnchorEl(e.currentTarget)
    setSessionAction({ open: true, session })
  }

  const handleAction = (e, type) => {
    e.stopPropagation()
    if (type === 'renameSession') setRenameSession(sessionAction.session)
    if (type === 'delete') setDeleteSession(sessionAction.session)
    setSessionAction({ open: false })
  }

  const handleSubmitRename = (e) => {
    e.stopPropagation()
    if (e.key === 'Enter') {
      const newChatSessions = chatSessions.map((item) => (item.id === renameSession.id ? renameSession : item))
      setChatSessions(newChatSessions)
      setRenameSession(null)
    }
  }

  const handleSubmitDelete = () => {
    deleteChatSession(deleteSession.id)
    setDeleteSession(null)
    if (chatSessionId === deleteSession.id) navigate(`/chat/${chatFlowId}`)
  }

  useEffect(() => {
    if (renameSession?.id) document.getElementById(renameSession.id)?.focus()
  }, [renameSession])

  return (
    <>
      <List
        className='p-2 pt-0 max-h-[calc(100vh-30%-56px)]'
        sx={{
          overflowY: 'auto',
          overflowX: 'hidden',
          scrollbarColor: '#d0d0d0 #ffffff00'
        }}
        // subheader={
        //   <ListSubheader component='div' className='bg-[#ebebeb] p-0 leading-8'>
        //     Lịch sử chat
        //   </ListSubheader>
        // }
      >
        {chatSessions?.map((session) => {
          if (session.type === 'temp') return null

          const isSelected = location.pathname.includes(session.id)
          return (
            <ListItemButton
              key={session.id}
              title={session.name}
              disableRipple={true}
              sx={{
                padding: '8px',
                borderRadius: '8px',
                background: isSelected ? '#d0d0d0' : 'transparent',
                '& .MuiListItemSecondaryAction-root': {
                  visibility: 'hidden'
                },
                '& .MuiListItemSecondaryAction-root:focus': {
                  visibility: 'visible'
                },
                '&:hover': {
                  background: '#e1e1e1',
                  '& .MuiListItemSecondaryAction-root': {
                    visibility: 'visible'
                  }
                }
              }}
              onClick={() => !isSelected && navigate(`/chat/${chatFlowId}/${session.id}`)}
            >
              <ListItemText className={clsx('m-0', renameSession?.id !== session.id && 'hidden')}>
                <input
                  id={session.id}
                  type='text'
                  value={renameSession?.name ?? ''}
                  onChange={(e) => setRenameSession((prev) => ({ ...prev, name: e.target.value }))}
                  onBlur={() => setRenameSession(null)}
                  onKeyUp={handleSubmitRename}
                  className='w-full h-6 border border-gray-400 border-solid outline-none'
                />
              </ListItemText>
              {renameSession?.id !== session.id && (
                <>
                  <ListItemText
                    sx={{
                      '& .MuiTypography-root': {
                        display: 'block',
                        textOverflow: 'ellipsis',
                        overflow: 'hidden',
                        whiteSpace: 'nowrap',
                        marginRight: '30px',
                        lineHeight: '24px'
                      }
                    }}
                    className='m-0'
                  >
                    {session.name}
                  </ListItemText>
                  <ListItemSecondaryAction className='flex'>
                    <IconDots size={20} onClick={(e) => handleSessionAction(e, session)} className='hover:text-[#0d0d0d] text-[#5d5d5d]' />
                  </ListItemSecondaryAction>
                </>
              )}
            </ListItemButton>
          )
        })}
        <Menu anchorEl={anchorEl} open={sessionAction.open} onClose={() => setSessionAction({ open: false })}>
          <MenuItem onClick={(e) => handleAction(e, 'renameSession')}>
            <Stack gap={1} direction='row' alignItems='center'>
              <IconPencil size={16} className='mb-1' />
              <Typography variant='body2'>{t('menu.rename')}</Typography>
            </Stack>
          </MenuItem>
          <MenuItem onClick={(e) => handleAction(e, 'delete')}>
            <Stack gap={1} direction='row' alignItems='center'>
              <IconTrash size={16} className='mb-1' color='red' />
              <Typography variant='body2' color='red'>
                {t('menu.delete')}
              </Typography>
            </Stack>
          </MenuItem>
        </Menu>
      </List>
      <Dialog open={!!deleteSession} onClose={() => setDeleteSession(null)}>
        <DialogTitle className='text-[18px]'>{t('menu.deleteChat')}</DialogTitle>
        <DialogContent>
          <DialogContentText className='text-[#3b3b3b]'>
            This action will delete <strong>{deleteSession?.name}</strong>
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button color='inherit' onClick={() => setDeleteSession(null)}>
            Cancel
          </Button>
          <Button color='error' onClick={handleSubmitDelete}>
            {t('menu.delete')}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  )
}

export default memo(ChatSessions)
