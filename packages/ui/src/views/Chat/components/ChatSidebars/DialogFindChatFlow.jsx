/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable jsx-a11y/no-autofocus */
import { useFlows } from '@/views/Chat/hooks/ChatHook'
import {
  AppBar,
  Avatar,
  Dialog,
  DialogContent,
  IconButton,
  InputBase,
  ListItemAvatar,
  ListItemButton,
  ListItemText,
  Toolbar
} from '@mui/material'
import { IconX } from '@tabler/icons-react'
import { debounce } from 'lodash'
import PropTypes from 'prop-types'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { ListBgColorWithTextWhite } from './data'
import { getChatflowDisplayName } from '../../../../utils/chatflowMapping'

const DialogFindChatFlow = ({ open, onClose }) => {
  const { t } = useTranslation()
  const refInput = useRef(null)

  const navigate = useNavigate()

  const [chatFlows] = useFlows()

  const [keyword, setKeyword] = useState('')
  const [filteredChatFlows, setFilteredChatFlows] = useState([])

  const handleKeyword = (value) => {
    setKeyword(value)
    handleFilteredChatFlows(value)
  }

  const handleFilteredChatFlows = useCallback(
    debounce((value) => {
      setFilteredChatFlows(
        chatFlows.filter((flow) => {
          // Search in both original name and mapped display name
          const displayName = getChatflowDisplayName(flow.id, t, flow.name)
          return flow.name.toLowerCase().includes(value.toLowerCase()) || displayName.toLowerCase().includes(value.toLowerCase())
        })
      )
    }, 300),
    [chatFlows, t]
  )

  const moveTo = useCallback(
    (url) => {
      navigate(url)
      onClose()
    },
    [navigate, onClose]
  )

  const ViewChatFlows = useMemo(() => {
    return filteredChatFlows?.map((flow, i) => {
      // Get display name using mapping or fallback to original name
      const displayName = getChatflowDisplayName(flow.id, t, flow.name)

      return (
        <ListItemButton
          key={flow.id}
          title={displayName}
          disableRipple={true}
          sx={{
            padding: '8px',
            borderRadius: '8px',
            background: 'transparent',
            '&:hover': {
              background: '#e1e1e1'
            }
          }}
          onClick={() => moveTo(`/chat/${flow.id}`)}
        >
          <ListItemAvatar className='min-w-11 m-0'>
            <Avatar
              variant='rounded'
              sx={{
                bgcolor: ListBgColorWithTextWhite[i % ListBgColorWithTextWhite.length],
                color: 'white',
                width: '32px',
                height: '32px',
                fontSize: '12px'
              }}
            >
              {displayName
                .toUpperCase()
                .replace(/[^A-Z1-9]/g, '')
                .slice(0, 3)}
            </Avatar>
          </ListItemAvatar>
          <ListItemText
            sx={{
              '& .MuiTypography-root': {
                display: 'block',
                textOverflow: 'ellipsis',
                overflow: 'hidden',
                whiteSpace: 'nowrap',
                fontSize: '16px'
              }
            }}
            className='m-0'
          >
            {displayName}
          </ListItemText>
        </ListItemButton>
      )
    })
  }, [filteredChatFlows, moveTo, t])

  useEffect(() => {
    if (open) {
      setTimeout(() => {
        if (refInput.current) {
          refInput.current.focus()
        }
      }, 100)
    }
  }, [open])

  useEffect(() => {
    if (chatFlows.length) setFilteredChatFlows(chatFlows)
  }, [chatFlows])

  return (
    <Dialog fullWidth maxWidth='sm' open={open} onClose={onClose}>
      <AppBar sx={{ position: 'relative', background: '#ffffff', boxShadow: 'none', borderBottom: '1px solid #0d0d0d1a' }}>
        <Toolbar sx={{ padding: '8px 16px' }}>
          <InputBase
            inputRef={refInput}
            value={keyword}
            placeholder={t('search.searchAgent')}
            sx={{ flex: 1, fontSize: '16px' }}
            onChange={(e) => handleKeyword(e.target.value)}
          />
          <IconButton edge='end' onClick={onClose}>
            <IconX />
          </IconButton>
        </Toolbar>
      </AppBar>
      <DialogContent sx={{ maxHeight: 'calc(100vh - 400px)', minHeight: 'calc(100vh - 600px)' }}>{ViewChatFlows}</DialogContent>
    </Dialog>
  )
}

DialogFindChatFlow.propTypes = {
  open: PropTypes.bool,
  onClose: PropTypes.func.isRequired
}

export default DialogFindChatFlow
