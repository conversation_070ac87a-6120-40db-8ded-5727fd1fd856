import chatFlowsApi from '@/api/chatflows'
import dayjs from 'dayjs'
import localforage from 'localforage'
import { debounce } from 'lodash'
import PropTypes from 'prop-types'
import { createContext, useCallback, useEffect, useMemo, useState } from 'react'
import { useNavigate, useParams } from 'react-router'
import { v4 } from 'uuid'

export const ChatContext = createContext(null)

const ChatProvider = ({ children }) => {
  const navigate = useNavigate()

  const { chatFlowId, chatSessionId } = useParams()

  const [isStreaming, setIsStreaming] = useState(false)
  const [chatMessages, setChatMessages] = useState([])
  const [chatSessions, setChatSessions] = useState([])
  const [chatFlows, setChatFlows] = useState([])
  const [sidebar, setSidebar] = useState({
    open: true,
    expChatFlows: dayjs().unix()
  })

  const handleIsStreaming = useCallback((value) => {
    setIsStreaming(value)
  }, [])

  const handleChatFlows = useCallback((value) => {
    setChatFlows(value)
    localforage.setItem('chatFlows', value)
  }, [])

  const handleChatSessions = useCallback(
    (value) => {
      setChatSessions(value)
      localforage.setItem(chatFlowId, value)
    },
    [chatFlowId]
  )

  const handleDeleteSession = useCallback(
    async (sessionId) => {
      const newSessions = chatSessions.filter((session) => session.id !== sessionId)
      setChatSessions(newSessions)
      await localforage.removeItem(sessionId)
      await localforage.setItem(chatFlowId, newSessions)
    },
    [chatFlowId, chatSessions]
  )

  const handleSidebar = useCallback(async (value) => {
    setSidebar((prev) => ({ ...prev, ...value }))
    const localSidebar = await localforage.getItem('sidebar')
    await localforage.setItem('sidebar', { ...localSidebar, ...value })
  }, [])

  const handleChatMessages = useCallback(
    async (value, options) => {
      if (chatMessages?.length === 1 && Array.isArray(value)) {
        const question = value[0].message
        const findSessionTemp = chatSessions.find((session) => session.type === 'temp')
        const newSessions = chatSessions
          ?.map((session) => {
            if (session.type === 'temp') {
              session.name = question
              session.type = 'session'
              session.date = dayjs().unix()
            }
            return session
          })
          .sort((a, b) => b.date - a.date)
        setChatSessions(newSessions)
        await localforage.setItem(chatFlowId, newSessions)
        navigate(`/chat/${chatFlowId}/${findSessionTemp.id}`)
      }

      setChatMessages((prev) => {
        let newMessages = prev
        const prevMessages = prev.slice(0, prev.length - (options.quantity || 1))

        if (options.doneResponse)
          newMessages = prev.map((session) => {
            if (session.status === 'responding') return { ...session, status: 'done' }
            return session
          })
        if (options.replaceRespondingMessage)
          newMessages = prev.map((session) => {
            if (session.status === 'responding') return { ...session, message: value.message }
            return session
          })
        if (options.removeLast) newMessages = prevMessages
        if (options.pushToLast) newMessages = [...prev, ...(Array.isArray(value) ? value : [value])]
        if (options.replaceLast) newMessages = [...prevMessages, value]

        return newMessages
      })
    },
    [chatMessages?.length, chatSessions, chatFlowId, navigate]
  )

  const data = useMemo(
    () => ({
      sidebar,
      isStreaming,
      chatFlows,
      chatSessions,
      chatMessages,
      handleSidebar,
      handleIsStreaming,
      handleChatFlows,
      handleChatSessions,
      handleChatMessages,
      handleDeleteSession
    }),
    [
      sidebar,
      isStreaming,
      chatFlows,
      chatSessions,
      chatMessages,
      handleSidebar,
      handleIsStreaming,
      handleChatFlows,
      handleChatSessions,
      handleChatMessages,
      handleDeleteSession
    ]
  )

  const updateLocalMessages = debounce(async (chatMessages) => {
    if (!chatSessionId || !chatMessages?.length) return
    await localforage.setItem(
      chatSessionId,
      chatMessages?.map((message) => (message.status === 'done' ? { ...message, status: '' } : message))
    )
    const isDoneLastMessage = chatMessages[chatMessages.length - 1].status === 'done'
    if (isDoneLastMessage) {
      const newSessions = chatSessions
        ?.map((session) => {
          if (session.id === chatSessionId) session.date = dayjs().unix()
          return session
        })
        .sort((a, b) => b.date - a.date)
      setChatSessions(newSessions)
      await localforage.setItem(chatFlowId, newSessions)
    }
  }, 500)

  const abortRespondingMessage = async () => {
    const keys = await localforage.keys()
    for await (const key of keys) {
      const value = await localforage.getItem(key)
      if (value?.length) {
        const findRespondingMessage = value.find((message) => message.status === 'responding')
        if (findRespondingMessage) {
          const updatedValue = findRespondingMessage?.message?.length
            ? [...value, { message: 'Có lỗi xảy ra trong quá trình trả lời, vui lòng thử lại.', type: 'abort' }].map((message) => {
                if (message.status === 'responding') return { ...message, type: 'done' }
                return message
              })
            : value.map((message) => {
                if (message.status === 'responding')
                  return { message: 'Có lỗi xảy ra trong quá trình trả lời, vui lòng thử lại.', type: 'abort' }
                return message
              })
          await localforage.setItem(key, updatedValue)
        }
      }
    }
  }

  const createNewSession = () => {
    let config = {}
    const configString = chatFlows.find((flow) => flow.id === chatFlowId)?.chatbotConfig
    if (configString) config = JSON.parse(configString)

    const id = v4()
    const welcomeMessage = config?.welcomeMessage || 'Hello, how can I help you?'
    const sessions = [
      {
        id,
        name: 'New chat',
        date: dayjs().unix(),
        type: 'temp'
      }
    ]
    const messages = [{ id: v4(), message: welcomeMessage, type: 'answer' }]

    return { sessions, messages }
  }

  const handleGetChatMessages = useCallback(async () => {
    await abortRespondingMessage()
    if (!chatSessionId) {
      setChatMessages([])
      return
    }

    const messages = await localforage.getItem(chatSessionId)
    if (messages?.length) {
      let flag = false
      const newMessages = messages.map((message) => {
        if (!message.id) {
          flag = true
          return { ...message, id: v4() }
        }
        return message
      })
      setChatMessages(newMessages)
      if (flag) await localforage.setItem(chatSessionId, newMessages)
    }
  }, [chatSessionId])

  const handleGetChatSessions = useCallback(async () => {
    await abortRespondingMessage()
    if (!chatFlowId) return

    let localSessions = await localforage.getItem(chatFlowId)
    localSessions = localSessions?.sort((a, b) => b.date - a.date) || []
    if (chatSessionId && localSessions?.length) {
      setChatSessions(localSessions)
    } else {
      const { sessions, messages } = createNewSession()
      setChatSessions([...sessions, ...localSessions])
      setChatMessages(messages)
      if (chatSessionId) navigate(`/chat/${chatFlowId}`)
    }
  }, [chatFlowId, chatSessionId, navigate])

  const handleGetChatFlows = useCallback(async () => {
    // const localChatFlows = await localforage.getItem('chatFlows')
    const localSidebar = await localforage.getItem('sidebar')
    // if (localChatFlows && localSidebar.expChatFlows > dayjs().unix()) {
    //   setChatFlows(localChatFlows)
    //   setSidebar(localSidebar)
    // } else {
    const res = await chatFlowsApi.getAllAgentflows()
    if (res.data?.data?.length) {
      const expChatFlows = dayjs().add(8, 'hours').unix()
      setChatFlows(res.data?.data)
      setSidebar((prev) => ({ ...prev, expChatFlows }))
      await localforage.setItem('chatFlows', res.data?.data)
      await localforage.setItem('sidebar', { ...localSidebar, expChatFlows, open: true })
    }
    // }
  }, [])

  useEffect(() => {
    const checkValidId = async () => {
      if (chatFlowId) {
        const localChatFlows = await localforage.getItem('chatFlows')
        const findChatFlow = localChatFlows?.find((flow) => flow.id === chatFlowId) ?? chatFlows.find((flow) => flow.id === chatFlowId)
        if (!findChatFlow) navigate(`/chat/translation`)
      }
    }
    checkValidId()
  }, [chatFlowId, chatFlows, navigate])

  useEffect(() => {
    if (chatMessages.length) updateLocalMessages(chatMessages)
  }, [chatMessages])

  useEffect(() => {
    handleGetChatMessages()
  }, [handleGetChatMessages])

  useEffect(() => {
    handleGetChatSessions()
  }, [handleGetChatSessions])

  useEffect(() => {
    handleGetChatFlows()
  }, [handleGetChatFlows])

  return <ChatContext.Provider value={data}>{children}</ChatContext.Provider>
}

ChatProvider.propTypes = {
  children: PropTypes.node
}

export default ChatProvider
