import { useState } from 'react'
import {
  <PERSON><PERSON>,
  TextField,
  Paper,
  Box,
  Grid,
  Typography,
  Container,
  InputAdornment,
  IconButton,
  useMediaQuery,
  createTheme,
  ThemeProvider,
  CircularProgress,
  Alert
} from '@mui/material'
import VisibilityOutlinedIcon from '@mui/icons-material/VisibilityOutlined'
import VisibilityOffOutlinedIcon from '@mui/icons-material/VisibilityOffOutlined'
import LoginIcon from '@mui/icons-material/Login'
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline'
import { motion } from 'framer-motion'
import userApi from '@/api/user'
import { useDispatch } from 'react-redux'
import VIBLogo from '@/assets/images/VIB.png'

import { useNavigate } from 'react-router-dom'
import { logoutAction } from '@/store/actions'
import PropTypes from 'prop-types'

const theme = createTheme({
  palette: {
    primary: {
      main: '#0066B3',
      light: '#4B96D1',
      dark: '#00478E',
      contrastText: '#fff'
    },
    secondary: {
      main: '#f50057',
      light: '#ff4081',
      dark: '#c51162',
      contrastText: '#fff'
    },
    background: {
      default: '#f5f5f5',
      paper: '#ffffff'
    },
    error: {
      main: '#f44336',
      light: '#e57373'
    }
  },
  typography: {
    fontFamily: '"Poppins", "Roboto", "Helvetica", "Arial", sans-serif',
    h4: {
      fontWeight: 700,
      letterSpacing: '0.03em'
    },
    h6: {
      fontWeight: 500,
      letterSpacing: '0.02em'
    },
    button: {
      fontWeight: 600,
      textTransform: 'none',
      letterSpacing: '0.03em'
    }
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: '0.5rem',
          padding: '0.625rem 1.5rem',
          boxShadow: '0 0.25rem 0.375rem rgba(50, 50, 93, 0.1), 0 0.0625rem 0.1875rem rgba(0, 0, 0, 0.08)',
          transition: 'all 0.3s ease',
          '&:hover': {
            transform: 'translateY(-0.125rem)',
            boxShadow: '0 0.5rem 1rem rgba(50, 50, 93, 0.15), 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1)'
          },
          '&:focus': {
            boxShadow: '0 0 0 2px rgba(63, 81, 181, 0.25), 0 0.25rem 0.375rem rgba(50, 50, 93, 0.1)'
          }
        },
        containedPrimary: {
          background: 'linear-gradient(45deg, #0066B3 30%, #4B96D1 90%)'
        }
      }
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            borderRadius: '0.5rem',
            transition: 'all 0.2s ease',
            '&:hover .MuiOutlinedInput-notchedOutline': {
              borderColor: '#3f51b5'
            },
            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
              borderWidth: '2px'
            }
          },
          '& .MuiInputLabel-root': {
            fontSize: '0.9rem',
            transform: 'translate(14px, 14px) scale(1)'
          },
          '& .MuiInputLabel-shrink': {
            transform: 'translate(14px, -6px) scale(0.75)'
          }
        }
      }
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          borderRadius: '1rem',
          boxShadow:
            '0 0.625rem 1.875rem rgba(0, 0, 0, 0.1), 0 0.25rem 0.5rem rgba(0, 0, 0, 0.06), inset 0 0 0 1px rgba(255, 255, 255, 0.1)',
          marginBottom: '1rem'
        }
      }
    },
    MuiAlert: {
      styleOverrides: {
        root: {
          borderRadius: '0.5rem',
          fontSize: '0.875rem'
        },
        standardError: {
          border: '1px solid rgba(244, 67, 54, 0.1)',
          backgroundColor: 'rgba(244, 67, 54, 0.05)'
        }
      }
    }
  }
})

const LoginVIB = ({ isLoginPage }) => {
  const [showPassword, setShowPassword] = useState(false)
  const [username, setUsername] = useState('')
  const [password, setPassword] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [msgError, setMsgError] = useState('')
  const dispatch = useDispatch()
  const navigate = useNavigate()

  const isMobile = useMediaQuery(theme.breakpoints.down('sm'))
  const logout = (...args) => dispatch(logoutAction(...args))

  const handleLogin = async (e) => {
    e.preventDefault()
    if (!username || !password) {
      const message = 'Vui lòng nhập tài khoản và mật khẩu.'
      setMsgError(message)
      return
    }
    setIsLoading(true)

    try {
      let resData = await userApi.loginUser({ username, password })
      resData = resData.data
      if (resData) {
        if (resData?.user?.groupname !== 'VIB' && resData?.user?.groupname !== 'Master_admin') {
          setMsgError('Chỉ tài khoản thuộc nhóm VIB mới có quyền truy cập vào hệ thống này.')
          localStorage.removeItem('dataLogin')
          logout({})
          sessionStorage.clear()
          return
        }
        localStorage.setItem('dataLogin', JSON.stringify(resData))

        if (isLoginPage) {
          window.location.href = '/'
        } else {
          navigate(0)
        }
      }
    } catch (error) {
      if (error?.response?.data?.message) {
        setMsgError(error?.response?.data?.message)
      } else {
        setMsgError('Đã có lỗi xảy ra. Vui lòng thử lại sau.')
      }
      localStorage.removeItem('dataLogin')
      sessionStorage.clear()
      logout({})
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <ThemeProvider theme={theme}>
      <Container
        component='main'
        maxWidth='lg'
        sx={{
          height: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          py: 2,
          overflow: 'auto'
        }}
      >
        <Grid
          container
          component={Paper}
          elevation={6}
          sx={{
            borderRadius: '1rem',
            overflow: 'hidden',
            boxShadow: '0 0.9375rem 3.125rem rgba(0, 0, 0, 0.1)',
            maxHeight: isMobile ? 'auto' : '35rem',
            height: isMobile ? 'auto' : '70vh',
            maxWidth: '62.5rem',
            width: '100%'
          }}
        >
          {/* Left side - Image */}
          {!isMobile && (
            <Grid
              item
              xs={false}
              sm={5}
              md={6}
              sx={{
                backgroundImage: 'url(https://source.unsplash.com/random?wallpapers)',
                backgroundRepeat: 'no-repeat',
                backgroundColor: (t) => (t.palette.mode === 'light' ? t.palette.grey[50] : t.palette.grey[900]),
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                position: 'relative',
                height: '100%'
              }}
            >
              <Box
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  p: { xs: 2, sm: 3, md: 4 },
                  textAlign: 'center',
                  backgroundSize: '#AEA3A333'
                }}
              >
                <Typography component='h1' variant='h3' color='white' fontWeight='bold' mb={2}>
                  <img src={VIBLogo} alt='VIB Logo' style={{ maxWidth: '100%', height: 'auto' }} />
                </Typography>
                <Typography className='font-normal text-[18px]' color='#0066B3' paragraph>
                  Ngân hàng quốc tế
                </Typography>

                <Typography className='font-normal text-[12px]' color='#0066B3' paragraph>
                  Chào mừng bạn đến với dịch vụ Chatbot của chúng tôi. Vui lòng đăng nhập để sử dụng dịch vụ.
                </Typography>
              </Box>
            </Grid>
          )}

          {/* Right side - Form */}
          <Grid
            item
            xs={12}
            sm={7}
            md={6}
            component={motion.div}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
            sx={{ height: isMobile ? 'auto' : '100%' }}
          >
            <Box
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'space-evenly',
                maxHeight: '70vh'
              }}
            >
              <Box
                component='form'
                noValidate
                onSubmit={handleLogin}
                className='max-md:px-3'
                sx={{ width: '100%', mt: 1, maxWidth: '25rem', mb: 2 }}
              >
                <div className='flex flex-col items-center mb-5  max-sm:mb-0'>
                  <Typography component='h1' variant='h4'>
                    Đăng Nhập
                  </Typography>
                </div>
                {msgError && (
                  <Alert
                    severity='error'
                    icon={<ErrorOutlineIcon fontSize='inherit' />}
                    sx={{
                      mb: 2,
                      borderRadius: '8px',
                      animation: 'fadeIn 0.3s ease-in-out',
                      '& .MuiAlert-icon': {
                        color: '#f44336'
                      },
                      '@keyframes fadeIn': {
                        '0%': { opacity: 0, transform: 'translateY(-10px)' },
                        '100%': { opacity: 1, transform: 'translateY(0)' }
                      }
                    }}
                  >
                    {msgError}
                  </Alert>
                )}

                <TextField
                  margin='normal'
                  required
                  fullWidth
                  id='username'
                  label='Tài khoản'
                  name='username'
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  InputLabelProps={{
                    shrink: true
                  }}
                  autoComplete='username'
                  aria-label='Nhập tên tài khoản'
                  sx={{ mb: 2 }}
                />
                <TextField
                  margin='normal'
                  required
                  fullWidth
                  name='password'
                  label='Mật Khẩu'
                  type={showPassword ? 'text' : 'password'}
                  id='password'
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  InputLabelProps={{
                    shrink: true
                  }}
                  autoComplete='current-password'
                  aria-label='Nhập mật khẩu'
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position='end'>
                        <IconButton
                          aria-label={showPassword ? 'Ẩn mật khẩu' : 'Hiện mật khẩu'}
                          onClick={() => setShowPassword(!showPassword)}
                          edge='end'
                        >
                          {showPassword ? <VisibilityOffOutlinedIcon /> : <VisibilityOutlinedIcon />}
                        </IconButton>
                      </InputAdornment>
                    )
                  }}
                  sx={{ mb: 2 }}
                />

                <Button
                  type='submit'
                  fullWidth
                  variant='contained'
                  color='primary'
                  size='large'
                  disabled={isLoading}
                  sx={{
                    py: 1.5,
                    fontSize: { xs: '0.875rem', sm: '1rem' }
                  }}
                  startIcon={isLoading ? <CircularProgress size={20} className='text-white' /> : <LoginIcon />}
                >
                  <span className='text-white'>{isLoading ? 'Đang xử lý...' : 'Đăng Nhập'}</span>
                </Button>
              </Box>
            </Box>
          </Grid>
        </Grid>
      </Container>
    </ThemeProvider>
  )
}

LoginVIB.propTypes = {
  isLoginPage: PropTypes.bool
}

export default LoginVIB
