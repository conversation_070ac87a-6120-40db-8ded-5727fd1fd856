import { useEffect, useState } from 'react'
import PropTypes from 'prop-types'
import { Button, CircularProgress, Box } from '@mui/material'
import { useTranslation } from 'react-i18next'
import useMSAL from '@/hooks/useMSAL'

const MicrosoftLoginButton = ({ onError = undefined, disabled = false, fullWidth = true }) => {
  const { t } = useTranslation()
  const [isLoading, setIsLoading] = useState(false)
  const { isInitialized, login, error: msalError } = useMSAL()

  // Clear OAuth flag on first render
  useEffect(() => {
    localStorage.removeItem('microsoftOAuthInProgress')
  }, [])

  const handleMicrosoftLogin = async () => {
    if (!isInitialized) {
      const errorMsg = t('microsoft.notReady')
      onError?.(errorMsg)
      return
    }

    if (isLoading) return

    setIsLoading(true)
    try {
      // Set flag to indicate Microsoft OAuth is in progress
      localStorage.setItem('microsoftOAuthInProgress', 'true')
      await login(['User.Read'])
      // Redirect will happen automatically
    } catch (error) {
      localStorage.removeItem('microsoftOAuthInProgress')
      onError?.(error.message || t('microsoft.loginFailed'))
    } finally {
      setIsLoading(false)
    }
  }

  // Error state
  if (msalError) {
    return (
      <Button
        fullWidth={fullWidth}
        variant='outlined'
        disabled
        sx={{
          mt: 1,
          py: 1.5,
          borderColor: '#f44336',
          color: '#f44336'
        }}
      >
        {t('microsoft.unavailable')}
      </Button>
    )
  }

  return (
    <Button
      fullWidth={fullWidth}
      variant='outlined'
      onClick={handleMicrosoftLogin}
      disabled={!isInitialized || isLoading}
      sx={{
        mt: 1,
        py: 1.5,
        borderColor: '#0078d4',
        color: '#0078d4',
        backgroundColor: 'white',
        '&:hover': {
          borderColor: '#106ebe',
          backgroundColor: 'rgba(0, 120, 212, 0.04)',
          color: '#106ebe'
        },
        '&:disabled': {
          borderColor: '#e0e0e0',
          color: '#9e9e9e'
        }
      }}
      startIcon={
        isLoading ? (
          <CircularProgress size={20} sx={{ color: '#0078d4' }} />
        ) : (
          <Box
            component='img'
            src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjEiIGhlaWdodD0iMjEiIHZpZXdCb3g9IjAgMCAyMSAyMSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3QgeD0iMSIgeT0iMSIgd2lkdGg9IjkiIGhlaWdodD0iOSIgZmlsbD0iI0Y0NTIzNiIvPgo8cmVjdCB4PSIxMSIgeT0iMSIgd2lkdGg9IjkiIGhlaWdodD0iOSIgZmlsbD0iIzAwQkNGMiIvPgo8cmVjdCB4PSIxIiB5PSIxMSIgd2lkdGg9IjkiIGhlaWdodD0iOSIgZmlsbD0iI0ZGQjkwMCIvPgo8cmVjdCB4PSIxMSIgeT0iMTEiIHdpZHRoPSI5IiBoZWlnaHQ9IjkiIGZpbGw9IiMwMEE5NTIiLz4KPC9zdmc+'
            alt='Microsoft'
            sx={{ width: 20, height: 20 }}
          />
        )
      }
    >
      {isLoading ? t('microsoft.processing') : t('microsoft.loginButton')}
    </Button>
  )
}

MicrosoftLoginButton.propTypes = {
  onError: PropTypes.func,
  disabled: PropTypes.bool,
  fullWidth: PropTypes.bool
}

export default MicrosoftLoginButton
