import React, { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Box, IconButton, Menu, MenuItem, Typography, Tooltip, ListItemIcon, ListItemText, Divider } from '@mui/material'
import { IconCheck } from '@tabler/icons-react'
import PropTypes from 'prop-types'

// SVG Flag Components
const VietnamFlag = ({ size = 18 }) => (
  <svg width={size} height={size * 0.67} viewBox='0 0 30 20' xmlns='http://www.w3.org/2000/svg'>
    <rect width='30' height='20' fill='#da251d' />
    <polygon points='15,4 16.18,8.82 21,8.82 17.41,11.59 18.59,16.41 15,13.64 11.41,16.41 12.59,11.59 9,8.82 13.82,8.82' fill='#ffff00' />
  </svg>
)

const USAFlag = ({ size = 18 }) => (
  <svg width={size} height={size * 0.67} viewBox='0 0 30 20' xmlns='http://www.w3.org/2000/svg'>
    <rect width='30' height='20' fill='#b22234' />
    <rect width='30' height='1.54' y='1.54' fill='#ffffff' />
    <rect width='30' height='1.54' y='4.62' fill='#ffffff' />
    <rect width='30' height='1.54' y='7.69' fill='#ffffff' />
    <rect width='30' height='1.54' y='10.77' fill='#ffffff' />
    <rect width='30' height='1.54' y='13.85' fill='#ffffff' />
    <rect width='30' height='1.54' y='16.92' fill='#ffffff' />
    <rect width='12' height='10.77' fill='#3c3b6e' />
  </svg>
)

const JapanFlag = ({ size = 18 }) => (
  <svg width={size} height={size * 0.67} viewBox='0 0 30 20' xmlns='http://www.w3.org/2000/svg'>
    <rect width='30' height='20' fill='#ffffff' stroke='#ddd' strokeWidth='0.5' />
    <circle cx='15' cy='10' r='6' fill='#bc002d' />
  </svg>
)

const ChinaFlag = ({ size = 18 }) => (
  <svg width={size} height={size * 0.67} viewBox='0 0 30 20' xmlns='http://www.w3.org/2000/svg'>
    <rect width='30' height='20' fill='#de2910' />
    <polygon points='6,3 7.09,6.18 10.5,6.18 7.91,8.32 8.91,11.5 6,9.36 3.09,11.5 4.09,8.32 1.5,6.18 4.91,6.18' fill='#ffde00' />
    <polygon points='12,2 12.5,3.5 14,3.5 12.75,4.5 13.25,6 12,5 10.75,6 11.25,4.5 10,3.5 11.5,3.5' fill='#ffde00' />
    <polygon points='14,5 14.5,6.5 16,6.5 14.75,7.5 15.25,9 14,8 12.75,9 13.25,7.5 12,6.5 13.5,6.5' fill='#ffde00' />
    <polygon points='14,9 14.5,10.5 16,10.5 14.75,11.5 15.25,13 14,12 12.75,13 13.25,11.5 12,10.5 13.5,10.5' fill='#ffde00' />
    <polygon points='12,12 12.5,13.5 14,13.5 12.75,14.5 13.25,16 12,15 10.75,16 11.25,14.5 10,13.5 11.5,13.5' fill='#ffde00' />
  </svg>
)

// PropTypes for flag components
VietnamFlag.propTypes = {
  size: PropTypes.number
}

USAFlag.propTypes = {
  size: PropTypes.number
}

JapanFlag.propTypes = {
  size: PropTypes.number
}

ChinaFlag.propTypes = {
  size: PropTypes.number
}

const LanguageSwitcher = ({ sx = {} }) => {
  const { i18n, t } = useTranslation()
  const [anchorEl, setAnchorEl] = useState(null)
  const open = Boolean(anchorEl)

  const languages = [
    { code: 'vi', name: 'Tiếng Việt', flag: <VietnamFlag size={18} /> },
    { code: 'en', name: 'English', flag: <USAFlag size={18} /> },
    { code: 'ja', name: '日本語', flag: <JapanFlag size={18} /> },
    { code: 'zh', name: '中文', flag: <ChinaFlag size={18} /> }
  ]

  const currentLanguage = languages.find((lang) => lang.code === i18n.language) || languages[0]

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget)
  }

  const handleClose = () => {
    setAnchorEl(null)
  }

  const handleLanguageChange = (languageCode) => {
    i18n.changeLanguage(languageCode)
    handleClose()
    // Reload page to update state after language change
    window.location.reload()
  }

  return (
    <Box sx={sx}>
      <Tooltip title={t('languages.changeLanguage')}>
        <IconButton
          onClick={handleClick}
          size='small'
          sx={{
            color: '#5f6368',
            '&:hover': {
              backgroundColor: 'rgba(95, 99, 104, 0.1)'
            },
            '& svg': {
              borderRadius: '2px',
              boxShadow: '0 1px 3px rgba(0,0,0,0.2)'
            }
          }}
        >
          {currentLanguage.flag}
        </IconButton>
      </Tooltip>

      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        slotProps={{
          paper: {
            sx: {
              minWidth: 200,
              mt: 1,
              boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
              border: '1px solid #e8eaed'
            }
          }
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <Box sx={{ px: 2, py: 1 }}>
          <Typography variant='caption' color='text.secondary' fontWeight={500}>
            {t('languages.selectLanguage')}
          </Typography>
        </Box>
        <Divider />

        {languages.map((language) => (
          <MenuItem
            key={language.code}
            onClick={() => handleLanguageChange(language.code)}
            selected={currentLanguage.code === language.code}
            sx={{
              py: 1.5,
              px: 2,
              '&.Mui-selected': {
                backgroundColor: 'rgba(26, 115, 232, 0.08)',
                '&:hover': {
                  backgroundColor: 'rgba(26, 115, 232, 0.12)'
                }
              }
            }}
          >
            <ListItemIcon sx={{ minWidth: 36, display: 'flex', alignItems: 'center', gap: 1 }}>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  '& svg': {
                    borderRadius: '2px',
                    boxShadow: '0 1px 2px rgba(0,0,0,0.1)'
                  }
                }}
              >
                {language.flag}
              </Box>
              {currentLanguage.code === language.code && <IconCheck size={16} color='#1a73e8' />}
            </ListItemIcon>
            <ListItemText>
              <Typography
                variant='body2'
                sx={{
                  fontWeight: currentLanguage.code === language.code ? 500 : 400,
                  color: currentLanguage.code === language.code ? '#1a73e8' : 'text.primary'
                }}
              >
                {language.name}
              </Typography>
            </ListItemText>
          </MenuItem>
        ))}
      </Menu>
    </Box>
  )
}

LanguageSwitcher.propTypes = {
  sx: PropTypes.object
}

export default LanguageSwitcher
