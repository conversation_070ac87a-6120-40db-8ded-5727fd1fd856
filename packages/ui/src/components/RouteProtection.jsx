import { useEffect } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate, useLocation } from 'react-router-dom'
import { isAdmin } from '@/utils/roleUtils'

/**
 * Route protection component that redirects non-admin users to /chat
 * when they try to access admin routes
 */
const RouteProtection = ({ children }) => {
  const user = useSelector((state) => state.user)
  const navigate = useNavigate()
  const location = useLocation()

  useEffect(() => {
    // Only check for admin routes (not chat routes)
    const isAdminRoute =
      location.pathname === '/' ||
      location.pathname.startsWith('/chatflows') ||
      location.pathname.startsWith('/agentflows') ||
      location.pathname.startsWith('/marketplaces') ||
      location.pathname.startsWith('/apikey') ||
      location.pathname.startsWith('/tools') ||
      location.pathname.startsWith('/assistants') ||
      location.pathname.startsWith('/credentials') ||
      location.pathname.startsWith('/variables') ||
      location.pathname.startsWith('/document-stores') ||
      location.pathname.startsWith('/admin-account') ||
      location.pathname.startsWith('/profile')

    // If user is on admin route but doesn't have admin privileges, redirect to chat
    if (isAdminRoute && user?.id && !isAdmin(user)) {
      navigate('/chat', { replace: true })
    }
  }, [user, location.pathname, navigate])

  return children
}

export default RouteProtection
