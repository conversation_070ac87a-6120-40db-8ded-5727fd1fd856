import { useState, useEffect, useRef } from 'react'
import { PublicClientApplication } from '@azure/msal-browser'

// Singleton MSAL instance
let globalMsalInstance = null
let globalInitializationPromise = null

const useMSAL = () => {
  const [msalInstance, setMsalInstance] = useState(null)
  console.log('🚀 ~ useMSAL.js:10 ~ useMSAL ~ msalInstance:', msalInstance)
  const [isInitialized, setIsInitialized] = useState(false)
  const [error, setError] = useState(null)
  const [isInteractionInProgress, setIsInteractionInProgress] = useState(false)
  const interactionInProgressRef = useRef(false)

  useEffect(() => {
    const initializeMSAL = async () => {
      try {
        // Kiểm tra instance đã được khởi tạo chưa
        if (globalMsalInstance && globalInitializationPromise) {
          try {
            await globalInitializationPromise
            // Verify the instance is properly initialized
            if (!globalMsalInstance.getConfiguration) {
              throw new Error('Global MSAL instance is not properly initialized')
            }
            setMsalInstance(globalMsalInstance)
            setIsInitialized(true)
            setError(null)
            console.log('[MSAL] Using existing initialized instance')
            return
          } catch (error) {
            console.warn('[MSAL] Failed to use existing instance, creating new one:', error)
            // Reset global variables and continue with new initialization
            globalMsalInstance = null
            globalInitializationPromise = null
          }
        }

        // Kiểm tra clientId
        const clientId = 'd92dd29c-bd92-4b44-85c9-0e15d8f00a6c'
        if (!clientId) {
          throw new Error('Thiếu cấu hình Microsoft OAuth. Vui lòng kiểm tra REACT_APP_MICROSOFT_CLIENT_ID trong file .env')
        }

        // Kiểm tra tenantId
        const tenantId = '5711f360-583f-446b-8462-a4f65f52a921'
        if (!tenantId) {
          throw new Error('Thiếu cấu hình Microsoft OAuth. Vui lòng kiểm tra REACT_APP_MICROSOFT_TENANT_ID trong file .env')
        }

        // Cấu hình MSAL
        const msalConfig = {
          auth: {
            clientId,
            authority: `https://login.microsoftonline.com/${tenantId}`,
            redirectUri: window.location.origin + '/redirect',
            navigateToLoginRequestUrl: false,
            postLogoutRedirectUri: window.location.origin
          },
          cache: {
            // cacheLocation: true,
            storeAuthStateInCookie: true
          },
          system: {
            allowNativeBroker: false, // Tắt native broker để tránh lỗi
            loggerOptions: {
              loggerCallback: (level, message, containsPii) => {
                if (containsPii) return
                switch (level) {
                  case 'Error':
                    console.error(`[MSAL] ${message}`)
                    break
                  case 'Warning':
                    console.warn(`[MSAL] ${message}`)
                    break
                  case 'Info':
                    console.info(`[MSAL] ${message}`)
                    break
                  default:
                    console.debug(`[MSAL] ${message}`)
                }
              }
            }
          }
        }

        // Khởi tạo instance MSAL
        const instance = new PublicClientApplication(msalConfig)

        // Lưu instance toàn cục
        globalMsalInstance = instance
        globalInitializationPromise = instance.initialize()

        try {
          await globalInitializationPromise
          console.log('[MSAL] Instance initialized successfully')
        } catch (initError) {
          throw new Error(`Không thể khởi tạo MSAL: ${initError.message}`)
        }

        // Ensure instance is fully initialized before proceeding
        if (!instance.getConfiguration) {
          throw new Error('MSAL instance is not properly initialized')
        }

        // Cập nhật state
        setMsalInstance(instance)
        setIsInitialized(true)
        setError(null)

        // Xóa trạng thái tương tác cũ - only after initialization is complete
        try {
          const interactionStatus = instance.getActiveAccount()
          if (interactionStatus) {
            instance.setActiveAccount(null)
          }
        } catch (clearError) {
          console.warn('Không thể xóa trạng thái tương tác:', clearError)
        }

        // Xử lý redirect nếu không ở trang redirect/login - only after initialization is complete
        if (!window.location.pathname.includes('/redirect') && !window.location.pathname.includes('/login')) {
          try {
            setIsInteractionInProgress(true)
            interactionInProgressRef.current = true
            await instance.handleRedirectPromise()
          } catch (redirectError) {
            console.warn('Lỗi xử lý redirect:', redirectError)
          } finally {
            setIsInteractionInProgress(false)
            interactionInProgressRef.current = false
          }
        }
      } catch (err) {
        // Xử lý lỗi
        globalMsalInstance = null
        globalInitializationPromise = null

        const errorMessage = err.errorMessage || err.message
        console.error('Lỗi khởi tạo MSAL:', errorMessage)

        setError({
          code: err.errorCode || 'unknown_error',
          message: errorMessage,
          timestamp: new Date().toISOString()
        })
        setIsInitialized(false)
        setIsInteractionInProgress(false)
        interactionInProgressRef.current = false

        // Thông báo lỗi chi tiết
        throw new Error(`Lỗi xác thực Microsoft: ${errorMessage}`)
      }
    }

    initializeMSAL()

    // Cleanup function
    return () => {
      if (globalMsalInstance) {
        try {
          globalMsalInstance.logout()
        } catch (error) {
          console.warn('Lỗi khi logout:', error)
        }
      }
    }
  }, [])

  const login = async (scopes = ['User.Read']) => {
    if (!isInitialized || !msalInstance) {
      throw new Error('MSAL chưa được khởi tạo')
    }
    console.log('111111')
    // Additional check to ensure instance is fully initialized
    if (!msalInstance.getConfiguration) {
      throw new Error('MSAL instance is not properly initialized')
    }

    if (isInteractionInProgress || interactionInProgressRef.current) {
      throw new Error('Đang có phiên đăng nhập Microsoft khác đang diễn ra. Vui lòng chờ...')
    }

    try {
      // Reset trạng thái
      setIsInteractionInProgress(false)
      interactionInProgressRef.current = false

      // Kiểm tra token hiện tại
      const accounts = msalInstance.getAllAccounts()
      if (accounts.length > 0) {
        try {
          const silentRequest = {
            scopes,
            account: accounts[0],
            forceRefresh: false
          }
          await msalInstance.acquireTokenSilent(silentRequest)
          return // Đã có token hợp lệ
        } catch (silentError) {
          // Token hết hạn hoặc không hợp lệ, tiếp tục login
          console.warn('Token silent refresh failed:', silentError)
        }
      }

      // Thực hiện login
      const loginRequest = {
        scopes,
        prompt: 'select_account' // Cho phép chọn tài khoản
      }

      setIsInteractionInProgress(true)
      interactionInProgressRef.current = true

      await msalInstance.loginRedirect(loginRequest)
    } catch (err) {
      setIsInteractionInProgress(false)
      interactionInProgressRef.current = false

      // Xử lý các lỗi cụ thể
      switch (err.errorCode) {
        case 'interaction_in_progress':
          try {
            await msalInstance.handleRedirectPromise()
          } catch (clearError) {
            console.warn('Không thể xóa trạng thái tương tác:', clearError)
          }
          throw new Error('Có phiên đăng nhập Microsoft đang diễn ra. Vui lòng thử lại sau.')

        case 'user_cancelled':
          throw new Error('Người dùng đã hủy đăng nhập')

        case 'access_denied':
          throw new Error('Quyền truy cập bị từ chối')

        default:
          console.error('Lỗi đăng nhập Microsoft:', err)
          throw new Error(`Lỗi đăng nhập: ${err.message}`)
      }
    }
  }

  const handleRedirectPromise = async () => {
    if (!isInitialized || !msalInstance) {
      throw new Error('MSAL chưa được khởi tạo')
    }

    // Additional check to ensure instance is fully initialized
    if (!msalInstance.getConfiguration) {
      throw new Error('MSAL instance is not properly initialized')
    }

    console.log('hhuhhuhu', msalInstance, msalInstance.handleRedirectPromise())

    try {
      setIsInteractionInProgress(true)
      interactionInProgressRef.current = true

      // Kiểm tra params trên URL
      const urlParams = new URLSearchParams(window.location.hash.substring(1))
      const hasCode = urlParams.has('code')
      const hasError = urlParams.has('error')
      const errorDescription = urlParams.get('error_description')

      if (hasError) {
        throw new Error(`Lỗi xác thực: ${errorDescription || 'Unknown error'}`)
      }

      let response = await msalInstance.handleRedirectPromise()
      console.log('🚀 ~ useMSAL.js:273 ~ handleRedirectPromise ~ response:', response)

      // Xử lý trường hợp có code nhưng không có response
      if (!response && hasCode && !hasError) {
        try {
          const accounts = msalInstance.getAllAccounts()
          if (accounts.length > 0) {
            const silentRequest = {
              scopes: ['User.Read'],
              account: accounts[0],
              forceRefresh: true
            }
            response = await msalInstance.acquireTokenSilent(silentRequest)
          }
        } catch (silentError) {
          console.warn('Lỗi lấy token:', silentError)
          throw new Error('Không thể lấy token xác thực')
        }
      }

      if (response?.account) {
        msalInstance.setActiveAccount(response.account)
      }

      return response
    } catch (err) {
      const errorMessage = err.errorMessage || err.message
      console.error('Lỗi xử lý redirect:', errorMessage)
      throw new Error(`Lỗi xử lý đăng nhập: ${errorMessage}`)
    } finally {
      setIsInteractionInProgress(false)
      interactionInProgressRef.current = false
    }
  }

  const clearCache = () => {
    if (!msalInstance) return

    try {
      // Xóa cache
      if (typeof msalInstance.clearCache === 'function') {
        msalInstance.clearCache()
      }

      // Xóa session storage
      sessionStorage.clear()

      // Xóa active account
      msalInstance.setActiveAccount(null)

      // Reset states
      setIsInitialized(false)
      setError(null)
      setIsInteractionInProgress(false)
      interactionInProgressRef.current = false
    } catch (error) {
      console.error('Lỗi xóa cache:', error)
      throw new Error('Không thể xóa cache đăng nhập')
    }
  }

  const getAccounts = () => {
    if (!msalInstance) return []
    try {
      return msalInstance.getAllAccounts()
    } catch (error) {
      console.warn('Lỗi lấy danh sách tài khoản:', error)
      return []
    }
  }

  return {
    msalInstance,
    isInitialized,
    error,
    login,
    handleRedirectPromise,
    isInteractionInProgress,
    clearCache,
    getAccounts
  }
}

export default useMSAL
