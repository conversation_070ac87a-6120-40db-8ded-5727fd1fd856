import { Button, IconButton, styled } from '@mui/material'
import PropTypes from 'prop-types'

const VisuallyHiddenInput = styled('input')({
  clip: 'rect(0 0 0 0)',
  clipPath: 'inset(50%)',
  height: 1,
  overflow: 'hidden',
  position: 'absolute',
  bottom: 0,
  left: 0,
  whiteSpace: 'nowrap',
  width: 1
})

const UploadButton = ({ type = 'button', icon, label = 'Tải lên', multiple = false, onChange }) => {
  if (type === 'button')
    return (
      <Button component='label' variant='contained' tabIndex={-1} startIcon={icon}>
        {label}
        <VisuallyHiddenInput type='file' multiple={multiple} onChange={onChange} />
      </Button>
    )
  return (
    <IconButton component='label' variant='contained' tabIndex={-1}>
      {icon}
      <VisuallyHiddenInput type='file' multiple={multiple} onChange={onChange} />
    </IconButton>
  )
}

UploadButton.propTypes = {
  type: PropTypes.string,
  icon: PropTypes.node,
  label: PropTypes.string,
  multiple: PropTypes.bool,
  onChange: PropTypes.func
}

export default UploadButton
