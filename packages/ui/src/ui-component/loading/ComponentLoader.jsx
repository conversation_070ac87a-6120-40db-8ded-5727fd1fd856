import { CircularProgress, Paper } from '@mui/material'
import clsx from 'clsx'
import PropTypes from 'prop-types'

const ComponentLoader = ({ loading = false, blur = true, transparent = false }) => {
  if (!loading) return null
  return (
    <Paper
      className={clsx(
        'absolute top-0 left-0 z-50 flex items-center justify-center w-full h-full',
        blur && 'bg-[#ffffff86]',
        transparent && 'bg-transparent',
        !blur && !transparent && 'bg-white'
      )}
    >
      <CircularProgress color='primary' />
    </Paper>
  )
}

ComponentLoader.propTypes = {
  loading: PropTypes.bool,
  blur: PropTypes.bool,
  transparent: PropTypes.bool
}

export default ComponentLoader
