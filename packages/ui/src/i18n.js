import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'
import LanguageDetector from 'i18next-browser-languagedetector'

// Import translation files
import viTranslations from './locales/vi.json'
import enTranslations from './locales/en.json'
import jaTranslations from './locales/ja.json'
import zhTranslations from './locales/zh.json'

const resources = {
  vi: {
    translation: viTranslations
  },
  en: {
    translation: enTranslations
  },
  ja: {
    translation: jaTranslations
  },
  zh: {
    translation: zhTranslations
  }
}

// Set default language to Vietnamese if not exists in localStorage
if (
  !localStorage.getItem('i18nextLng') ||
  (localStorage.getItem('i18nextLng') !== 'vi' &&
    localStorage.getItem('i18nextLng') !== 'en' &&
    localStorage.getItem('i18nextLng') !== 'ja' &&
    localStorage.getItem('i18nextLng') !== 'zh')
) {
  console.log("🚀 ~ i18n.js:33 ~ localStorage.getItem('i18nextLng'):", localStorage.getItem('i18nextLng'))
  localStorage.setItem('i18nextLng', 'vi')
}

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: 'vi', // Default to Vietnamese
    debug: false,

    detection: {
      order: ['localStorage', 'navigator', 'htmlTag'],
      caches: ['localStorage'],
      lookupLocalStorage: 'i18nextLng'
    },

    interpolation: {
      escapeValue: false // React already does escaping
    },

    react: {
      useSuspense: false
    }
  })

export default i18n
